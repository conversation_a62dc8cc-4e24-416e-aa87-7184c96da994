# Report Requirements Analysis - What's Actually Needed

## ❌ CRITICAL ISSUE: Previous ER Diagram Was Incomplete

The simplified ER diagram I created earlier **CANNOT support all three reports**. After analyzing the actual report requirements, **ACD processing is NOT optional** - it's required for ALL reports.

## Required Metrics Analysis

### Report 1: ACD - Detailed Calls by Group
**❌ MISSING from call data only:**
- **Wrap-up Time** - Requires agent state tracking (Available → Wrap-up → Available)
- **Staffed Time** - Requires agent login/logout session data  
- **Avg Agents Logged In** - Requires agent session data per time period
- **Agents Logged In (list)** - Requires knowing which agents were logged into each queue

**✅ Available from call data:**
- Calls Answered, Talk Time, Handling Time, Service Level, Transferred In/Out

### Report 2: ACD - Call Queue Summary Report  
**❌ MISSING from call data only:**
- **Logged in Time** - Requires agent session duration data
- **Group Utilization** - Formula: `(Total Call Time / Total Logged in Time) * 100`
- **Agents Logged In** - Requires agent session data

**✅ Available from call data:**
- Calls, Service Level %, Abandoned calls

### Report 3: ACD - Call Taking Group Overview
**❌ MISSING from call data only:**
- **Available Time** - Requires agent state tracking (Available state duration)
- **Group Utilization** - Formula: `(Available Time / Logged in Time) * 100`

**✅ Available from call data:**
- Service Level breakdowns, Calls Answered/Abandoned/Transferred

## Data Sources Required

### From Call Data (raw_callsummary):
- ✅ Call volume metrics
- ✅ Service level flags (pre-calculated)
- ✅ Call timing (talk time, handling time)
- ✅ Call outcomes (abandoned, transferred)

### From ACD Events (REQUIRED):
- ❌ **Agent Login/Logout** - For session duration and "logged in time"
- ❌ **Agent State Changes** - For available time, wrap-up time tracking
- ❌ **Queue Assignments** - For knowing which agents are in which queues
- ❌ **Agent Availability** - For utilization calculations

## Corrected Table Requirements

### Essential Tables (ALL REQUIRED):
1. **`raw_callsummary`** - Call data from Collector API
2. **`fact_calls`** - Transformed call data
3. **`dim_agents`** - Agent master data
4. **`dim_queues`** - Queue master data  
5. **`dim_time`** - Time dimension
6. **`fact_agent_sessions`** - Agent login/logout sessions (**REQUIRED**)
7. **`fact_agent_states`** - Agent state changes (**REQUIRED**)
8. **`fact_agent_intervals`** - Aggregated agent metrics (**REQUIRED**)

### What Each Table Provides:

#### fact_agent_sessions
- **Logged in Time**: Session duration per agent per queue
- **Agents Logged In**: Which agents were active in each queue
- **Staffed Time**: Total agent availability for utilization calculations

#### fact_agent_states  
- **Available Time**: Duration in "Available" state
- **Wrap-up Time**: Duration in "Wrap-up" state after calls
- **Busy Time**: Duration in "Busy" or "On Call" state

#### fact_agent_intervals
- **Aggregated Metrics**: Pre-calculated 5-minute or hourly rollups
- **Utilization Calculations**: Available time / Logged in time ratios
- **Performance Metrics**: Calls handled per agent per interval

## Architecture Implications

### ❌ CANNOT Simplify to Call Data Only
The three reports require **real-time agent state tracking**, which means:

1. **ACD Processor Lambda** - REQUIRED (not optional)
2. **Agent Session Tables** - REQUIRED (not optional)  
3. **Agent State Tables** - REQUIRED (not optional)
4. **Interval Aggregation** - REQUIRED (not optional)

### ✅ Correct Implementation Approach

**Phase 1 (6-8 weeks):**
1. Implement ACD Processor Lambda for agent events
2. Create all required tables (8 tables total)
3. Implement Data Transformer + Interval Aggregator Lambdas
4. Build reports with complete metrics

**Phase 2 (2-4 weeks):**
1. Add real-time dashboards
2. Optimize performance
3. Add advanced analytics

## Key Formulas That Require ACD Data

### Group Utilization
```
Group Utilization % = (Total Call Time / Total Logged in Time) * 100
```
- **Total Call Time**: From fact_calls (available)
- **Total Logged in Time**: From fact_agent_sessions (**REQUIRED**)

### Available Time Percentage  
```
Available Time % = (Available Time / Logged in Time) * 100
```
- **Available Time**: From fact_agent_states (**REQUIRED**)
- **Logged in Time**: From fact_agent_sessions (**REQUIRED**)

### Staffed Time
```
Staffed Time = Sum of all agent session durations per time period
```
- **Session Durations**: From fact_agent_sessions (**REQUIRED**)

## Conclusion

**The simplified approach will NOT work** for the three required reports. All reports need agent session and state data, which requires:

1. ✅ **ACD Processing Pipeline** - Process Login, Logout, Available, BusiedOut events
2. ✅ **Agent Session Tracking** - Track login/logout times per queue
3. ✅ **Agent State Tracking** - Track Available, Wrap-up, Busy states
4. ✅ **Interval Aggregation** - Calculate utilization metrics

The architecture must include **full ACD processing from Day 1** to deliver the required reports.
