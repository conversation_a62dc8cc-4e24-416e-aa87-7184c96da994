# Report Data Mapping - Field Sources and Aggregations

## Data Sources and Field Mapping

### Report 1: ACD - Detailed Calls by Group

| Report Field | Friendly Name | Data Source | Source Field(s) | Aggregation Method |
|-------------|---------------|-------------|-----------------|-------------------|
| **acdRingGroup** | Ring Group | `dim_queues` | `queue_name` | From `tenant_psap_name` in call data |
| **callsAnswered** | Answered | `fact_calls` | `is_abandoned = false` | `COUNT(*) WHERE NOT is_abandoned` |
| **transferredIn** | Transferred In | `fact_calls` | `transfer_to` | `SUM(CASE WHEN transfer_to = queue_name THEN 1 ELSE 0 END)` |
| **transferredOut** | Transferred Out | `fact_calls` | `transfer_from` | `SUM(CASE WHEN transfer_from = queue_name THEN 1 ELSE 0 END)` |
| **loggedInTime** | Staffed Time | `fact_agent_sessions` | `session_duration_seconds` | `SUM(session_duration_seconds) / 3600` (hours) |
| **availableTime** | Available Time | `fact_agent_states` | `state_duration_seconds WHERE state_type='Available'` | `SUM(state_duration_seconds) / 3600` (hours) |
| **wrapupTime** | Wrap-up Time | `fact_agent_states` | `state_duration_seconds WHERE state_type='Wrap-up'` | `SUM(state_duration_seconds) / 60` (minutes) |
| **talkTime** | Talk Time | `fact_calls` | `talk_time_seconds` | `SUM(talk_time_seconds) / 3600` (hours) |
| **handlingTime** | Handling Time | `fact_calls` | `total_call_time_seconds` | `SUM(total_call_time_seconds) / 60` (minutes) |
| **reasonCode** | Reason Code | `fact_agent_states` | `busied_out_action` | `STRING_AGG(DISTINCT busied_out_action)` |
| **agentsLoggedIn** | Agents Logged In | `fact_agent_sessions` | `agent_name` | `STRING_AGG(DISTINCT agent_name)` |
| **callsAnsweredIn10s%** | Service Level | `fact_calls` | `answered_within_10s` | `AVG(CASE WHEN answered_within_10s THEN 100.0 ELSE 0.0 END)` |

### Report 2: ACD - Call Queue Summary Report

| Report Field | Friendly Name | Data Source | Source Field(s) | Aggregation Method |
|-------------|---------------|-------------|-----------------|-------------------|
| **Queue Name** | Queue X Name | `dim_queues` | `queue_name` | Direct field |
| **calls** | Calls | `fact_calls` | `call_fact_id` | `COUNT(*)` total calls per queue |
| **callsAnsweredIn10s%** | Service Level | `fact_calls` | `answered_within_10s` | `AVG(CASE WHEN answered_within_10s THEN 100.0 ELSE 0.0 END)` |
| **callsAbandoned** | Abandoned | `fact_calls` | `is_abandoned = true` | `SUM(CASE WHEN is_abandoned THEN 1 ELSE 0 END)` |
| **loggedInTime** | Logged in Time | `fact_agent_sessions` | `session_duration_seconds` | `SUM(session_duration_seconds) / 3600` (hours) |
| **agentsLoggedIn** | Agents Logged In | `fact_agent_sessions` | `agent_key` | `COUNT(DISTINCT agent_key)` per time period |
| **groupUtilization** | Group Utilization% | `fact_calls` + `fact_agent_sessions` | `total_call_time_seconds` / `session_duration_seconds` | `(SUM(total_call_time_seconds) / SUM(session_duration_seconds)) * 100` |

### Report 3: ACD - Call Taking Group Overview

| Report Field | Friendly Name | Data Source | Source Field(s) | Aggregation Method |
|-------------|---------------|-------------|-----------------|-------------------|
| **acdGroup** | ACD Group | `dim_queues` | `queue_name` | Direct field |
| **calls** | Calls | `fact_calls` | `call_fact_id` | `COUNT(*)` total calls |
| **callsAnsweredWithin10s** | Answered ≤10s | `fact_calls` | `answered_within_10s = true` | `SUM(CASE WHEN answered_within_10s THEN 1 ELSE 0 END)` |
| **callsAnsweredWithin15s** | Answered ≤15s | `fact_calls` | `answered_within_15s = true` | `SUM(CASE WHEN answered_within_15s THEN 1 ELSE 0 END)` |
| **callsAnsweredWithin20s** | Answered ≤20s | `fact_calls` | `answered_within_20s = true` | `SUM(CASE WHEN answered_within_20s THEN 1 ELSE 0 END)` |
| **callsAnsweredWithin40s** | Answered ≤40s | `fact_calls` | `answered_within_40s = true` | `SUM(CASE WHEN answered_within_40s THEN 1 ELSE 0 END)` |
| **callsAnsweredAbove40s** | Answered >40s | `fact_calls` | `answered_within_40s = false` | `SUM(CASE WHEN NOT answered_within_40s THEN 1 ELSE 0 END)` |
| **callsAbandoned** | Abandoned | `fact_calls` | `is_abandoned = true` | `SUM(CASE WHEN is_abandoned THEN 1 ELSE 0 END)` |
| **callsTransferred** | Transferred | `fact_calls` | `is_transferred = true` | `SUM(CASE WHEN is_transferred THEN 1 ELSE 0 END)` |
| **callsAnsweredWithin10s%** | Service Level 10s | `fact_calls` | `answered_within_10s` | `AVG(CASE WHEN answered_within_10s THEN 100.0 ELSE 0.0 END)` |
| **callsAnsweredWithin15s%** | Service Level 15s | `fact_calls` | `answered_within_15s` | `AVG(CASE WHEN answered_within_15s THEN 100.0 ELSE 0.0 END)` |
| **loggedInTime** | Logged in Time | `fact_agent_sessions` | `session_duration_seconds` | `SUM(session_duration_seconds) / 60` (minutes) |
| **availableTime** | Available Time | `fact_agent_states` | `state_duration_seconds WHERE state_type='Available'` | `SUM(state_duration_seconds) / 60` (minutes) |
| **groupUtilization%** | Group Utilization | `fact_agent_states` + `fact_agent_sessions` | Available time / Logged in time | `(SUM(available_time) / SUM(logged_in_time)) * 100` |

## Data Source Origins

### From Call Data (raw_callsummary → fact_calls)
- **Source**: Existing MariaDB callsummary table via Collector API
- **Fields**: All call metrics, service level flags, talk times, transfer info
- **Pre-calculated**: Service level boolean flags (answered_within_10s, 15s, 20s, 40s)

### From ACD Events (Login/Logout → fact_agent_sessions)
- **Source**: ACD Login/Logout events from i3logs
- **Fields**: `agent`, `agentRole`, `tenantGroup`, `operatorId`, `workstation`, `ringGroupName`
- **Calculated**: Session duration = logout_time - login_time

### From ACD Events (Available/BusiedOut → fact_agent_states)
- **Source**: AgentAvailable/AgentBusiedOut events from i3logs
- **Fields**: `agent`, `busiedOutAction` (Manual, Break, Training, WrapUpFixedDuration)
- **Calculated**: State duration = next_state_time - current_state_time

### From ACD Events (ACDLogin/ACDLogout → fact_agent_sessions)
- **Source**: ACDLogin/ACDLogout events from i3logs
- **Fields**: `ringGroupName`, `ringGroupUri` for queue-specific sessions
- **Purpose**: Links agents to specific queues for accurate queue membership

## Key Aggregation Formulas

### Group Utilization (Report 2)
```sql
(SUM(fact_calls.total_call_time_seconds) / SUM(fact_agent_sessions.session_duration_seconds)) * 100
```
- **Numerator**: Total time agents spent on calls
- **Denominator**: Total time agents were logged in

### Group Utilization (Report 3) 
```sql
(SUM(available_time_seconds) / SUM(logged_in_time_seconds)) * 100
```
- **Numerator**: Total time agents were available for calls
- **Denominator**: Total time agents were logged in

### Service Level Calculations
```sql
AVG(CASE WHEN answered_within_Xs THEN 100.0 ELSE 0.0 END)
```
- Uses pre-calculated boolean flags from MariaDB
- Averages across all calls to get percentage

## Missing Data Handling

### Agent Data Without ACD Events
- **Fallback**: Use agent_name from call data to create basic dim_agents record
- **Limitation**: No role, workstation, or detailed session data

### Call Data Without Agent Sessions
- **Handling**: LEFT JOIN in Power BI views
- **Impact**: Utilization calculations may be incomplete

### Queue Associations
- **Primary**: Use ringGroupName from ACDLogin events
- **Fallback**: Use tenant_psap_name from call data
