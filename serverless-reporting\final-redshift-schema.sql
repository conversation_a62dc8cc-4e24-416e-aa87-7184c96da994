-- Final ACD/Agent Reporting Data Warehouse Schema
-- Star Schema Design for Power BI Optimization
-- Each client gets a separate schema for data isolation

-- =====================================================
-- DIMENSION TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS dim_agents (
    agent_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_id VARCHAR(100) NOT NULL,
    agent_name VARCHAR(200),
    agent_role VARCHAR(200),
    tenant_group VARCHAR(200),
    operator_id VARCHAR(50),
    workstation VARCHAR(100),
    client_code VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, client_code)
) 
DISTKEY(agent_key) 
SORTKEY(client_code, agent_id);

CREATE TABLE IF NOT EXISTS dim_queues (
    queue_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    queue_id VARCHAR(100) NOT NULL,
    queue_name VARCHAR(200),
    queue_uri VARCHAR(500),
    tenant_group VARCHAR(200),
    client_code VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(queue_id, client_code)
) 
DISTKEY(queue_key) 
SORTKEY(client_code, queue_id);

CREATE TABLE IF NOT EXISTS dim_time (
    time_key BIGINT PRIMARY KEY,
    full_datetime TIMESTAMP,
    full_date DATE,
    year_num INTEGER,
    month_num INTEGER,
    day_num INTEGER,
    hour_num INTEGER,
    minute_num INTEGER,
    interval_5min INTEGER, -- 0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55
    day_of_week INTEGER,
    day_name VARCHAR(20),
    month_name VARCHAR(20),
    quarter_num INTEGER,
    is_weekend BOOLEAN,
    is_business_hour BOOLEAN
) 
DISTSTYLE ALL;

-- =====================================================
-- RAW TABLES - DIRECT FROM COLLECTOR API
-- =====================================================

-- Raw callsummary table - direct copy from Collector API writes
CREATE TABLE IF NOT EXISTS raw_callsummary (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL, -- CustomerName
    tenant_psap_name VARCHAR(100) NOT NULL, -- tenantPsapName
    call_identifier VARCHAR(200), -- callIdentifier
    call_details_index INTEGER, -- callDetailsIndex
    is_abandoned_state SMALLINT, -- isAbandonedState
    address VARCHAR(500), -- address
    is_admin_call SMALLINT, -- isAdminCall
    is_admin_emergency_call SMALLINT, -- isAdminEmergencyCall
    agent_answered_within_10s SMALLINT, -- agentAnsweredWithin10s
    agent_answered_within_15s SMALLINT, -- agentAnsweredWithin15s
    agent_answered_within_20s SMALLINT, -- agentAnsweredWithin20s
    agent_answered_within_40s SMALLINT, -- agentAnsweredWithin40s
    agent_answered_more_than_10s SMALLINT, -- agentAnsweredMoreThan10s
    agent_answered_more_than_20s SMALLINT, -- agentAnsweredMoreThan20s
    agent_answered_more_than_40s SMALLINT, -- agentAnsweredMoreThan40s
    agent_callback_number VARCHAR(50), -- agentCallbacknumber
    agent_name VARCHAR(200), -- agentName
    agent_time_to_answer_seconds DECIMAL(10,2), -- agentTimeToAnswerInSeconds
    answered_by_system SMALLINT, -- answeredBySystem
    start_call_time TIMESTAMP, -- startCallTime
    start_call_time_local TIMESTAMP, -- startCallTimeToLocal
    end_call_time TIMESTAMP, -- endCallTime
    end_call_time_local TIMESTAMP, -- endCallTimeToLocal
    call_answered TIMESTAMP, -- callAnswered
    call_answered_local TIMESTAMP, -- callAnsweredToLocal
    call_presented TIMESTAMP, -- callPresented
    call_presented_local TIMESTAMP, -- callPresentedToLocal
    call_released TIMESTAMP, -- callReleased
    call_released_local TIMESTAMP, -- callReleasedToLocal
    call_transferred TIMESTAMP, -- callTransferred
    call_transferred_local TIMESTAMP, -- callTransferredToLocal
    call_back_number VARCHAR(50), -- callBackNumber
    call_mobility_type VARCHAR(50), -- callMobilityType
    call_state VARCHAR(50), -- callState
    call_type VARCHAR(50), -- callType
    carrier VARCHAR(50), -- carrier
    is_completed SMALLINT, -- isCompleted
    confidence DECIMAL(10,2), -- confidence
    is_emergency_call SMALLINT, -- isEmergencyCall
    esn DECIMAL(15,0), -- esn
    final_cos VARCHAR(50), -- finalCos
    hold_time_seconds DECIMAL(10,2), -- holdTimeInSeconds
    is_in_progress SMALLINT, -- isInProgress
    incident_identifier VARCHAR(200), -- incidentIdentifier
    is_abandoned SMALLINT, -- isAbandoned
    is_abandoned_callback SMALLINT, -- isAbandonedCallback
    is_admin SMALLINT, -- isAdmin
    is_admin_emergency SMALLINT, -- isAdminEmergency
    is_callback SMALLINT, -- isCallback
    is_emergency SMALLINT, -- isEmergency
    is_internal_transfer_call SMALLINT, -- isInternalTransferCall
    is_transferred SMALLINT, -- isTransferred
    time_stamp TIMESTAMP, -- timeStamp
    time_stamp_local TIMESTAMP, -- timeStampToLocal
    time_to_answer_seconds DECIMAL(10,2), -- timeToAnswerInSeconds
    non_emergency_time_to_answer_seconds DECIMAL(10,2), -- nonEmergencyTimeToAnswerInSeconds
    time_to_transfer_seconds DECIMAL(10,2), -- timeToTransferInSeconds
    total_call_time_seconds DECIMAL(10,2), -- totalCallTimeInSeconds
    non_emergency_total_call_time_seconds DECIMAL(10,2), -- nonEmergencyTotalCallTimeInSeconds
    transfer_from VARCHAR(200), -- transferFrom
    transfer_to VARCHAR(200), -- transferTo
    uncertainty DECIMAL(10,2), -- uncertainty
    zipcode VARCHAR(50), -- zipcode
    location_data_list VARCHAR(MAX), -- locationDataList (JSON)
    date_created TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Client Information
    client_code VARCHAR(20) NOT NULL,

    UNIQUE(call_identifier, customer_name)
)
DISTKEY(agent_name)
SORTKEY(client_code, start_call_time);

-- =====================================================
-- FACT TABLES - BUILT FROM RAW DATA VIA STORED PROCEDURES
-- =====================================================

CREATE TABLE IF NOT EXISTS fact_calls (
    call_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    call_identifier VARCHAR(200) NOT NULL,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    start_time_key BIGINT REFERENCES dim_time(time_key),
    end_time_key BIGINT REFERENCES dim_time(time_key),

    -- Call Timing
    start_call_time TIMESTAMP,
    end_call_time TIMESTAMP,
    call_answered TIMESTAMP,
    call_presented TIMESTAMP,
    call_released TIMESTAMP,
    call_transferred TIMESTAMP,

    -- Service Level Flags (from raw_callsummary)
    answered_within_10s BOOLEAN,
    answered_within_15s BOOLEAN,
    answered_within_20s BOOLEAN,
    answered_within_40s BOOLEAN,

    -- Call Metrics
    agent_time_to_answer_seconds DECIMAL(10,2),
    time_to_answer_seconds DECIMAL(10,2),
    total_call_time_seconds DECIMAL(10,2),
    hold_time_seconds DECIMAL(10,2),
    talk_time_seconds DECIMAL(10,2), -- Calculated: total_call_time - hold_time

    -- Call Flags
    is_abandoned BOOLEAN,
    is_transferred BOOLEAN,
    is_emergency BOOLEAN,
    is_admin BOOLEAN,
    is_completed BOOLEAN,

    -- Transfer Information
    transfer_from VARCHAR(200),
    transfer_to VARCHAR(200),

    -- Call Details
    call_type VARCHAR(100),
    call_state VARCHAR(100),
    address VARCHAR(500),
    zipcode VARCHAR(50),
    call_back_number VARCHAR(50),

    -- Client Information
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(call_identifier, client_code)
)
DISTKEY(agent_key)
SORTKEY(client_code, start_call_time);

-- =====================================================
-- FACT TABLES - ACD/AGENT DATA (from ACD Events Pipeline)
-- =====================================================

CREATE TABLE IF NOT EXISTS fact_agent_sessions (
    session_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    login_time_key BIGINT REFERENCES dim_time(time_key),
    logout_time_key BIGINT REFERENCES dim_time(time_key),
    login_timestamp TIMESTAMP,
    logout_timestamp TIMESTAMP,
    session_duration_seconds INTEGER,
    device_name VARCHAR(100),
    reason_code VARCHAR(100),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) 
DISTKEY(agent_key) 
SORTKEY(client_code, login_timestamp);

CREATE TABLE IF NOT EXISTS fact_acd_sessions (
    acd_session_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    login_time_key BIGINT REFERENCES dim_time(time_key),
    logout_time_key BIGINT REFERENCES dim_time(time_key),
    login_timestamp TIMESTAMP,
    logout_timestamp TIMESTAMP,
    session_duration_seconds INTEGER,
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) 
DISTKEY(agent_key) 
SORTKEY(client_code, login_timestamp);

CREATE TABLE IF NOT EXISTS fact_agent_states (
    state_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    state_timestamp TIMESTAMP,
    state_type VARCHAR(50), -- Available, BusiedOut, Break, Training, etc.
    state_duration_seconds INTEGER,
    reason_code VARCHAR(100),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) 
DISTKEY(agent_key) 
SORTKEY(client_code, state_timestamp);

-- =====================================================
-- 5-MINUTE INTERVAL TABLES (Real-time Aggregations)
-- =====================================================

CREATE TABLE IF NOT EXISTS fact_queue_intervals (
    interval_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    interval_start TIMESTAMP,
    interval_end TIMESTAMP,
    calls_offered INTEGER DEFAULT 0,
    calls_answered INTEGER DEFAULT 0,
    calls_abandoned INTEGER DEFAULT 0,
    calls_transferred_in INTEGER DEFAULT 0,
    calls_transferred_out INTEGER DEFAULT 0,
    agents_logged_in INTEGER DEFAULT 0,
    agents_available INTEGER DEFAULT 0,
    agents_on_call INTEGER DEFAULT 0,
    agents_in_wrap INTEGER DEFAULT 0,
    agents_unavailable INTEGER DEFAULT 0,
    avg_answer_time_seconds DECIMAL(8,2),
    avg_abandon_time_seconds DECIMAL(8,2),
    avg_talk_time_seconds DECIMAL(8,2),
    avg_wrap_time_seconds DECIMAL(8,2),
    service_level_10s DECIMAL(5,2),
    service_level_15s DECIMAL(5,2),
    service_level_20s DECIMAL(5,2),
    service_level_40s DECIMAL(5,2),
    longest_waiting_call_seconds INTEGER DEFAULT 0,
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(queue_key, interval_start)
) 
DISTKEY(queue_key) 
SORTKEY(client_code, interval_start);

CREATE TABLE IF NOT EXISTS fact_agent_intervals (
    agent_interval_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    interval_start TIMESTAMP,
    interval_end TIMESTAMP,
    calls_answered INTEGER DEFAULT 0,
    calls_transferred_in INTEGER DEFAULT 0,
    calls_transferred_out INTEGER DEFAULT 0,
    calls_on_hold INTEGER DEFAULT 0,
    staffed_time_seconds INTEGER DEFAULT 0, -- Time logged into queue
    available_time_seconds INTEGER DEFAULT 0, -- Time in Available state
    talk_time_seconds INTEGER DEFAULT 0,
    wrap_time_seconds INTEGER DEFAULT 0,
    hold_time_seconds INTEGER DEFAULT 0,
    unavailable_time_seconds INTEGER DEFAULT 0, -- Break, Training, etc.
    primary_reason_code VARCHAR(100),
    utilization_percentage DECIMAL(5,2), -- (talk + wrap) / staffed * 100
    occupancy_percentage DECIMAL(5,2), -- talk / available * 100
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_key, queue_key, interval_start)
) 
DISTKEY(agent_key) 
SORTKEY(client_code, interval_start);

-- =====================================================
-- REAL-TIME STATUS TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS real_time_queue_status (
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    last_updated TIMESTAMP,
    current_calls_waiting INTEGER DEFAULT 0,
    current_agents_logged_in INTEGER DEFAULT 0,
    current_agents_available INTEGER DEFAULT 0,
    current_agents_on_call INTEGER DEFAULT 0,
    longest_waiting_seconds INTEGER DEFAULT 0,
    current_service_level DECIMAL(5,2),
    calls_answered_last_5min INTEGER DEFAULT 0,
    calls_abandoned_last_5min INTEGER DEFAULT 0,
    avg_answer_time_last_5min DECIMAL(8,2),
    client_code VARCHAR(20) NOT NULL,
    PRIMARY KEY (queue_key, client_code)
) 
DISTKEY(queue_key) 
SORTKEY(client_code, last_updated);

CREATE TABLE IF NOT EXISTS real_time_agent_status (
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    last_updated TIMESTAMP,
    current_state VARCHAR(50),
    state_start_time TIMESTAMP,
    state_duration_seconds INTEGER,
    current_queue VARCHAR(100),
    current_call_id VARCHAR(100),
    is_available BOOLEAN DEFAULT false,
    reason_code VARCHAR(100),
    last_call_end_time TIMESTAMP,
    client_code VARCHAR(20) NOT NULL,
    PRIMARY KEY (agent_key, client_code)
) 
DISTKEY(agent_key) 
SORTKEY(client_code, last_updated);

-- =====================================================
-- POWER BI OPTIMIZED VIEWS (Star Schema)
-- =====================================================

-- =====================================================
-- STORED PROCEDURES FOR DATA TRANSFORMATION
-- =====================================================

-- Procedure to transform raw_callsummary into fact_calls
CREATE OR REPLACE PROCEDURE sp_transform_callsummary_to_facts()
AS $$
BEGIN
    -- Insert new call records from raw_callsummary
    INSERT INTO fact_calls (
        call_identifier, agent_key, queue_key, start_time_key, end_time_key,
        start_call_time, end_call_time, call_answered, call_presented, call_released, call_transferred,
        answered_within_10s, answered_within_15s, answered_within_20s, answered_within_40s,
        agent_time_to_answer_seconds, time_to_answer_seconds, total_call_time_seconds,
        hold_time_seconds, talk_time_seconds,
        is_abandoned, is_transferred, is_emergency, is_admin, is_completed,
        transfer_from, transfer_to, call_type, call_state, address, zipcode, call_back_number,
        client_code, created_date, updated_date
    )
    SELECT
        rcs.call_identifier,
        da.agent_key,
        dq.queue_key,
        dt_start.time_key,
        dt_end.time_key,
        rcs.start_call_time,
        rcs.end_call_time,
        rcs.call_answered,
        rcs.call_presented,
        rcs.call_released,
        rcs.call_transferred,
        CASE WHEN rcs.agent_answered_within_10s = 1 THEN true ELSE false END,
        CASE WHEN rcs.agent_answered_within_15s = 1 THEN true ELSE false END,
        CASE WHEN rcs.agent_answered_within_20s = 1 THEN true ELSE false END,
        CASE WHEN rcs.agent_answered_within_40s = 1 THEN true ELSE false END,
        rcs.agent_time_to_answer_seconds,
        rcs.time_to_answer_seconds,
        rcs.total_call_time_seconds,
        rcs.hold_time_seconds,
        GREATEST(0, rcs.total_call_time_seconds - COALESCE(rcs.hold_time_seconds, 0)) as talk_time_seconds,
        CASE WHEN rcs.is_abandoned = 1 THEN true ELSE false END,
        CASE WHEN rcs.is_transferred = 1 THEN true ELSE false END,
        CASE WHEN rcs.is_emergency = 1 THEN true ELSE false END,
        CASE WHEN rcs.is_admin = 1 THEN true ELSE false END,
        CASE WHEN rcs.is_completed = 1 THEN true ELSE false END,
        rcs.transfer_from,
        rcs.transfer_to,
        rcs.call_type,
        rcs.call_state,
        rcs.address,
        rcs.zipcode,
        rcs.call_back_number,
        rcs.client_code,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    FROM raw_callsummary rcs
    LEFT JOIN dim_agents da ON rcs.agent_name = da.agent_name AND rcs.client_code = da.client_code
    LEFT JOIN dim_queues dq ON rcs.tenant_psap_name = dq.queue_name AND rcs.client_code = dq.client_code
    LEFT JOIN dim_time dt_start ON DATE_TRUNC('hour', rcs.start_call_time) = dt_start.full_datetime
    LEFT JOIN dim_time dt_end ON DATE_TRUNC('hour', rcs.end_call_time) = dt_end.full_datetime
    WHERE NOT EXISTS (
        SELECT 1 FROM fact_calls fc
        WHERE fc.call_identifier = rcs.call_identifier
        AND fc.client_code = rcs.client_code
    );

    -- Create missing agents from callsummary data
    INSERT INTO dim_agents (agent_id, agent_name, client_code, created_date, updated_date)
    SELECT DISTINCT
        rcs.agent_name,
        rcs.agent_name,
        rcs.client_code,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    FROM raw_callsummary rcs
    WHERE rcs.agent_name IS NOT NULL
    AND NOT EXISTS (
        SELECT 1 FROM dim_agents da
        WHERE da.agent_name = rcs.agent_name
        AND da.client_code = rcs.client_code
    );

    -- Create missing queues from callsummary data
    INSERT INTO dim_queues (queue_id, queue_name, client_code, created_date, updated_date)
    SELECT DISTINCT
        rcs.tenant_psap_name,
        rcs.tenant_psap_name,
        rcs.client_code,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    FROM raw_callsummary rcs
    WHERE rcs.tenant_psap_name IS NOT NULL
    AND NOT EXISTS (
        SELECT 1 FROM dim_queues dq
        WHERE dq.queue_name = rcs.tenant_psap_name
        AND dq.client_code = rcs.client_code
    );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- POWER BI OPTIMIZED VIEWS (Updated for new schema)
-- =====================================================

-- Report 1: ACD - Detailed Calls by Group
CREATE VIEW view_acd_detailed_calls AS
SELECT
    COALESCE(dq.queue_name, 'Unknown Queue') as ring_group,
    COUNT(fc.call_key) as calls_answered,
    SUM(CASE WHEN fc.is_transferred AND fc.transfer_to IS NOT NULL THEN 1 ELSE 0 END) as transferred_in,
    SUM(CASE WHEN fc.is_transferred AND fc.transfer_from IS NOT NULL THEN 1 ELSE 0 END) as transferred_out,
    SUM(fc.talk_time_seconds) / 3600.0 as talk_time_hours,
    SUM(COALESCE(fai.wrap_time_seconds, 0)) / 3600.0 as wrap_time_hours,
    SUM(fc.total_call_time_seconds) / 3600.0 as handling_time_hours,
    AVG(COALESCE(fai.staffed_time_seconds, 0)) / 3600.0 as avg_agents_logged_in,
    AVG(CASE WHEN fc.answered_within_10s THEN 100.0 ELSE 0.0 END) as service_level_10s_percent,
    STRING_AGG(DISTINCT COALESCE(da.agent_id, 'Unknown'), ', ') as agents_logged_in,
    fc.client_code
FROM fact_calls fc
LEFT JOIN dim_queues dq ON fc.queue_key = dq.queue_key
LEFT JOIN dim_agents da ON fc.agent_key = da.agent_key
LEFT JOIN fact_agent_intervals fai ON da.agent_key = fai.agent_key
    AND dq.queue_key = fai.queue_key
    AND DATE(fc.start_call_time) = DATE(fai.interval_start)
WHERE NOT COALESCE(fc.is_abandoned, false)
GROUP BY COALESCE(dq.queue_name, 'Unknown Queue'), fc.client_code;

-- Report 2: ACD - Call Queue Summary Report
CREATE VIEW view_queue_summary AS
SELECT
    COALESCE(dq.queue_name, 'Unknown Queue') as queue_name,
    COUNT(fc.call_key) as calls,
    AVG(CASE WHEN fc.answered_within_10s THEN 100.0 ELSE 0.0 END) as calls_answered_in_10s_percent,
    SUM(CASE WHEN COALESCE(fc.is_abandoned, false) THEN 1 ELSE 0 END) as abandoned,
    SUM(COALESCE(fas.session_duration_seconds, 0)) / 3600.0 as logged_in_time_hours,
    COUNT(DISTINCT da.agent_key) as agents_logged_in,
    CASE
        WHEN SUM(COALESCE(fas.session_duration_seconds, 0)) > 0
        THEN (SUM(COALESCE(fc.total_call_time_seconds, 0)) * 100.0) / SUM(fas.session_duration_seconds)
        ELSE 0
    END as group_utilization_percent,
    fc.client_code
FROM fact_calls fc
LEFT JOIN dim_queues dq ON fc.queue_key = dq.queue_key
LEFT JOIN dim_agents da ON fc.agent_key = da.agent_key
LEFT JOIN fact_acd_sessions fas ON da.agent_key = fas.agent_key
    AND dq.queue_key = fas.queue_key
    AND DATE(fc.start_call_time) = DATE(fas.login_timestamp)
GROUP BY COALESCE(dq.queue_name, 'Unknown Queue'), fc.client_code;

-- Report 3: ACD - Call Taking Group Overview
CREATE VIEW view_call_taking_overview AS
SELECT
    COALESCE(dq.queue_name, 'Unknown Queue') as acd_group,
    COUNT(fc.call_key) as calls,
    SUM(CASE WHEN COALESCE(fc.answered_within_10s, false) THEN 1 ELSE 0 END) as calls_answered_within_10s,
    SUM(CASE WHEN COALESCE(fc.answered_within_15s, false) THEN 1 ELSE 0 END) as calls_answered_within_15s,
    SUM(CASE WHEN COALESCE(fc.answered_within_20s, false) THEN 1 ELSE 0 END) as calls_answered_within_20s,
    SUM(CASE WHEN COALESCE(fc.answered_within_40s, false) THEN 1 ELSE 0 END) as calls_answered_within_40s,
    SUM(CASE WHEN COALESCE(fc.is_abandoned, false) THEN 1 ELSE 0 END) as calls_abandoned,
    SUM(CASE WHEN COALESCE(fc.is_transferred, false) THEN 1 ELSE 0 END) as calls_transferred,
    AVG(CASE WHEN COALESCE(fc.answered_within_10s, false) THEN 100.0 ELSE 0.0 END) as calls_answered_within_10s_percent,
    AVG(CASE WHEN COALESCE(fc.answered_within_15s, false) THEN 100.0 ELSE 0.0 END) as calls_answered_within_15s_percent,
    SUM(COALESCE(fas.session_duration_seconds, 0)) / 60.0 as logged_in_time_minutes,
    SUM(COALESCE(fai.available_time_seconds, 0)) / 60.0 as available_time_minutes,
    CASE
        WHEN SUM(COALESCE(fas.session_duration_seconds, 0)) > 0
        THEN (SUM(COALESCE(fai.available_time_seconds, 0)) * 100.0) / SUM(fas.session_duration_seconds)
        ELSE 0
    END as group_utilization_percent,
    fc.client_code
FROM fact_calls fc
LEFT JOIN dim_queues dq ON fc.queue_key = dq.queue_key
LEFT JOIN dim_agents da ON fc.agent_key = da.agent_key
LEFT JOIN fact_acd_sessions fas ON da.agent_key = fas.agent_key
    AND dq.queue_key = fas.queue_key
    AND DATE(fc.start_call_time) = DATE(fas.login_timestamp)
LEFT JOIN fact_agent_intervals fai ON da.agent_key = fai.agent_key
    AND dq.queue_key = fai.queue_key
    AND DATE(fc.start_call_time) = DATE(fai.interval_start)
GROUP BY COALESCE(dq.queue_name, 'Unknown Queue'), fc.client_code;

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

CREATE INDEX idx_fact_calls_start_time ON fact_calls(call_start_time);
CREATE INDEX idx_fact_calls_client_date ON fact_calls(client_code, call_start_time);
CREATE INDEX idx_fact_agent_sessions_login ON fact_agent_sessions(login_timestamp);
CREATE INDEX idx_fact_acd_sessions_login ON fact_acd_sessions(login_timestamp);
CREATE INDEX idx_fact_agent_states_timestamp ON fact_agent_states(state_timestamp);
CREATE INDEX idx_fact_queue_intervals_start ON fact_queue_intervals(interval_start);
CREATE INDEX idx_fact_agent_intervals_start ON fact_agent_intervals(interval_start);
