# Essential Tables for Phase 1 - ACD Reporting

## Summary: Minimal Tables for Maximum Value

For the **first implementation**, we only need **5 tables** to deliver the three required reports:

### ✅ REQUIRED TABLES (Phase 1)

#### 1. Raw Data Table
- **`raw_callsummary`** - Direct write from Collector API
- **Purpose**: Single source of truth for all call data
- **Source**: Collector API writes directly (no MariaDB)

#### 2. Fact Table  
- **`fact_calls`** - Transformed call data with foreign keys
- **Purpose**: Star schema optimized for reporting
- **Contains**: All call metrics, service levels, timing data

#### 3. Essential Dimensions (3 tables)
- **`dim_agents`** - Agent master data (from agent_name in calls)
- **`dim_queues`** - Queue master data (from tenant_psap_name in calls)  
- **`dim_time`** - Time dimension for temporal grouping

### ❌ NOT NEEDED (Phase 1)

#### Real-time Tables
- ❌ `real_time_agent_status` - No real-time requirements initially

#### ACD Tables (Deferred to Phase 2)
- ❌ `fact_agent_sessions` - Agent login/logout sessions
- ❌ `fact_acd_sessions` - Agent queue assignments  
- ❌ `fact_agent_states` - Agent state changes
- ❌ `fact_agent_intervals` - 5-minute aggregations

## What Each Report Can Deliver (Phase 1)

### Report 1: ACD - Detailed Calls by Group
**✅ Available Metrics:**
- Calls Answered, Abandoned, Transferred
- Talk Time, Handling Time (from call data)
- Service Level (10s, 15s, 20s, 40s thresholds)
- Transfer In/Out counts

**❌ Missing (Phase 2):**
- Staffed Time, Available Time, Wrap-up Time
- Agent utilization percentages

### Report 2: ACD - Call Queue Summary Report  
**✅ Available Metrics:**
- Total Calls per queue
- Service Level percentages
- Abandoned call counts
- Average handling time

**❌ Missing (Phase 2):**
- Logged in Time
- Group Utilization percentages

### Report 3: ACD - Call Taking Group Overview
**✅ Available Metrics:**
- Service Level breakdowns (multiple thresholds)
- Call volume by group
- Abandoned/Transferred counts

**❌ Missing (Phase 2):**
- Available Time
- Group Utilization percentages

## Data Sources Available in Phase 1

### From MariaDB callsummary table (now in raw_callsummary):
- ✅ **Agent Name**: `agentName` field
- ✅ **Queue Name**: `tenantPsapName` field  
- ✅ **Service Levels**: Pre-calculated boolean flags
  - `agentAnsweredWithin10s`
  - `agentAnsweredWithin15s` 
  - `agentAnsweredWithin20s`
  - `agentAnsweredWithin40s`
- ✅ **Call Timing**: 
  - `totalCallTimeInSeconds`
  - `holdTimeInSeconds`
  - `agentTimeToAnswerInSeconds`
- ✅ **Call Outcomes**:
  - `isAbandoned`
  - `isTransferred` 
  - `isCompleted`
  - `isEmergency`
- ✅ **Transfer Data**:
  - `transferFrom`
  - `transferTo`

### What's Missing (Requires ACD Events in Phase 2):
- ❌ **Agent Sessions**: Login/logout times
- ❌ **Agent States**: Available, BusiedOut, etc.
- ❌ **Queue Assignments**: Which agents are logged into which queues
- ❌ **Utilization Metrics**: Available time, staffed time percentages

## Implementation Benefits

### Phase 1 Advantages:
1. **Quick Win**: Deliver reports in 4-6 weeks
2. **Low Risk**: Minimal changes to existing systems
3. **Immediate Value**: Most important call metrics available
4. **Foundation**: Sets up data pipeline for Phase 2

### Phase 2 Additions:
1. **Complete Metrics**: Add missing utilization data
2. **Real-time**: Live agent status and dashboards  
3. **Advanced Analytics**: 5-minute intervals, trend analysis

## Architecture Simplification

### What We Removed:
- ❌ **Dual Writes**: Collector API writes only to Redshift (not MariaDB + Redshift)
- ❌ **Real-time Processing**: No immediate ACD event processing
- ❌ **Complex Aggregations**: No 5-minute interval tables
- ❌ **Multiple Lambdas**: Only one Data Transformer Lambda needed

### What We Kept:
- ✅ **Existing Infrastructure**: S3 → SNS → SQS → Lambda → Collector API
- ✅ **Star Schema**: Proper dimensional modeling for Power BI
- ✅ **Scheduled Transformation**: Reliable batch processing
- ✅ **Client Isolation**: Separate schemas per client

This simplified approach delivers **80% of the value with 20% of the complexity**!
