# Power BI Import Queries for ACD Reporting

## Overview

This document provides the exact SQL queries needed to import data into Power BI for all 5 ACD reports. Each query is optimized for **Import Mode** with **hourly refresh cycles**.

---

## Report 1: ACD - Detailed Calls by Group

### Power BI Import Query
```sql
SELECT
    -- Primary Grouping
    COALESCE(dq.queue_name, 'Unknown Queue') as ring_group,
    fc.client_code,
    
    -- Call Volume Metrics
    COUNT(fc.call_key) as calls_answered,
    SUM(CASE WHEN fc.transfer_to = dq.queue_name THEN 1 ELSE 0 END) as transferred_in,
    SUM(CASE WHEN fc.transfer_from = dq.queue_name THEN 1 ELSE 0 END) as transferred_out,
    
    -- Time Metrics (converted to appropriate units)
    SUM(fas.session_duration_seconds) / 3600.0 as staffed_time_hours,
    SUM(fai.available_time_seconds) / 3600.0 as available_time_hours,
    SUM(fai.wrap_time_seconds) / 60.0 as wrap_time_minutes,
    SUM(fc.talk_time_seconds) / 3600.0 as talk_time_hours,
    SUM(fc.total_call_time_seconds) / 3600.0 as handling_time_hours,
    
    -- Agent Information
    STRING_AGG(DISTINCT da.agent_name, ', ') as agents_logged_in,
    COUNT(DISTINCT da.agent_key) as unique_agents_count,
    
    -- Service Level Metrics
    AVG(CASE WHEN fc.answered_within_10s THEN 100.0 ELSE 0.0 END) as service_level_10s_percent,
    
    -- Date for filtering
    DATE(fc.start_call_time) as call_date
    
FROM fact_calls fc
JOIN dim_queues dq ON fc.queue_key = dq.queue_key
JOIN dim_agents da ON fc.agent_key = da.agent_key
LEFT JOIN fact_acd_sessions fas ON da.agent_key = fas.agent_key 
    AND dq.queue_key = fas.queue_key
    AND DATE(fc.start_call_time) = DATE(fas.login_timestamp)
LEFT JOIN fact_agent_intervals fai ON da.agent_key = fai.agent_key 
    AND dq.queue_key = fai.queue_key
    AND DATE(fc.start_call_time) = DATE(fai.interval_start)
WHERE NOT COALESCE(fc.is_abandoned, false)
GROUP BY 
    COALESCE(dq.queue_name, 'Unknown Queue'), 
    fc.client_code,
    DATE(fc.start_call_time)
ORDER BY call_date DESC, ring_group
```

---

## Report 2: ACD - Call Queue Summary Report

### Power BI Import Query
```sql
SELECT
    -- Primary Grouping
    COALESCE(dq.queue_name, 'Unknown Queue') as queue_name,
    fc.client_code,
    
    -- Call Volume Metrics
    COUNT(fc.call_key) as calls,
    SUM(CASE WHEN COALESCE(fc.is_abandoned, false) THEN 1 ELSE 0 END) as calls_abandoned,
    
    -- Service Level Metrics
    AVG(CASE WHEN fc.answered_within_10s THEN 100.0 ELSE 0.0 END) as calls_answered_in_10s_percent,
    
    -- Agent Metrics
    SUM(COALESCE(fas.session_duration_seconds, 0)) / 3600.0 as logged_in_time_hours,
    COUNT(DISTINCT da.agent_key) as agents_logged_in,
    
    -- Utilization Calculation
    CASE 
        WHEN SUM(COALESCE(fas.session_duration_seconds, 0)) > 0 
        THEN (SUM(COALESCE(fc.total_call_time_seconds, 0)) * 100.0) / SUM(fas.session_duration_seconds)
        ELSE 0 
    END as group_utilization_percent,
    
    -- Date for filtering
    DATE(fc.start_call_time) as call_date,
    EXTRACT(HOUR FROM fc.start_call_time) as call_hour
    
FROM fact_calls fc
JOIN dim_queues dq ON fc.queue_key = dq.queue_key
JOIN dim_agents da ON fc.agent_key = da.agent_key
LEFT JOIN fact_acd_sessions fas ON da.agent_key = fas.agent_key 
    AND dq.queue_key = fas.queue_key
    AND DATE(fc.start_call_time) = DATE(fas.login_timestamp)
GROUP BY 
    COALESCE(dq.queue_name, 'Unknown Queue'), 
    fc.client_code,
    DATE(fc.start_call_time),
    EXTRACT(HOUR FROM fc.start_call_time)
ORDER BY call_date DESC, call_hour, queue_name
```

---

## Report 3: ACD - Call Taking Group Overview

### Power BI Import Query
```sql
SELECT
    -- Primary Grouping
    COALESCE(dq.queue_name, 'Unknown Queue') as acd_group,
    fc.client_code,
    
    -- Call Volume Metrics
    COUNT(fc.call_key) as calls,
    SUM(CASE WHEN COALESCE(fc.is_abandoned, false) THEN 1 ELSE 0 END) as calls_abandoned,
    SUM(CASE WHEN COALESCE(fc.is_transferred, false) THEN 1 ELSE 0 END) as calls_transferred,
    
    -- Service Level Breakdown
    SUM(CASE WHEN COALESCE(fc.answered_within_10s, false) THEN 1 ELSE 0 END) as calls_answered_within_10s,
    SUM(CASE WHEN COALESCE(fc.answered_within_15s, false) THEN 1 ELSE 0 END) as calls_answered_within_15s,
    SUM(CASE WHEN COALESCE(fc.answered_within_20s, false) THEN 1 ELSE 0 END) as calls_answered_within_20s,
    SUM(CASE WHEN COALESCE(fc.answered_within_40s, false) THEN 1 ELSE 0 END) as calls_answered_within_40s,
    
    -- Service Level Percentages
    AVG(CASE WHEN COALESCE(fc.answered_within_10s, false) THEN 100.0 ELSE 0.0 END) as calls_answered_within_10s_percent,
    AVG(CASE WHEN COALESCE(fc.answered_within_15s, false) THEN 100.0 ELSE 0.0 END) as calls_answered_within_15s_percent,
    
    -- Time Metrics (in minutes for this report)
    SUM(COALESCE(fas.session_duration_seconds, 0)) / 60.0 as logged_in_time_minutes,
    SUM(COALESCE(fai.available_time_seconds, 0)) / 60.0 as available_time_minutes,
    
    -- Group Utilization Calculation
    CASE 
        WHEN SUM(COALESCE(fas.session_duration_seconds, 0)) > 0 
        THEN (SUM(COALESCE(fai.available_time_seconds, 0)) * 100.0) / SUM(fas.session_duration_seconds)
        ELSE 0 
    END as group_utilization_percent,
    
    -- Date for filtering
    DATE(fc.start_call_time) as call_date
    
FROM fact_calls fc
JOIN dim_queues dq ON fc.queue_key = dq.queue_key
JOIN dim_agents da ON fc.agent_key = da.agent_key
LEFT JOIN fact_acd_sessions fas ON da.agent_key = fas.agent_key 
    AND dq.queue_key = fas.queue_key
    AND DATE(fc.start_call_time) = DATE(fas.login_timestamp)
LEFT JOIN fact_agent_intervals fai ON da.agent_key = fai.agent_key 
    AND dq.queue_key = fai.queue_key
    AND DATE(fc.start_call_time) = DATE(fai.interval_start)
GROUP BY 
    COALESCE(dq.queue_name, 'Unknown Queue'), 
    fc.client_code,
    DATE(fc.start_call_time)
ORDER BY call_date DESC, acd_group
```

---

## Report 4: Agent Performance - Call Distribution

### Power BI Import Query
```sql
SELECT
    -- Agent Information
    da.agent_name,
    da.agent_role,
    da.workstation,
    da.operator_id,
    
    -- Queue Information
    COALESCE(dq.queue_name, 'Unknown Queue') as acd_group,
    fc.client_code,
    
    -- Call Metrics (Average per shift)
    COUNT(fc.call_key) as total_calls,
    COUNT(fc.call_key) / NULLIF(COUNT(DISTINCT DATE(fc.start_call_time)), 0) as avg_calls_per_shift,
    
    -- Time Metrics
    SUM(COALESCE(fai.available_time_seconds, 0)) / 3600.0 as available_time_hours,
    COUNT(DISTINCT CASE WHEN fai.available_time_seconds > 0 THEN fai.agent_key END) as agent_count,
    
    -- Service Level Metrics
    AVG(CASE WHEN fc.answered_within_10s THEN 100.0 ELSE 0.0 END) as answered_in_10s_percent,
    AVG(CASE WHEN fc.answered_within_15s THEN 100.0 ELSE 0.0 END) as answered_in_15s_percent,
    AVG(CASE WHEN fc.answered_within_20s THEN 100.0 ELSE 0.0 END) as answered_in_20s_percent,
    AVG(CASE WHEN fc.answered_within_40s THEN 100.0 ELSE 0.0 END) as answered_in_40s_percent,
    AVG(CASE WHEN NOT COALESCE(fc.answered_within_40s, false) THEN 100.0 ELSE 0.0 END) as answered_greater_40s_percent,
    
    -- Date Range
    MIN(DATE(fc.start_call_time)) as first_call_date,
    MAX(DATE(fc.start_call_time)) as last_call_date,
    COUNT(DISTINCT DATE(fc.start_call_time)) as total_shifts
    
FROM dim_agents da
JOIN fact_calls fc ON da.agent_key = fc.agent_key
JOIN dim_queues dq ON fc.queue_key = dq.queue_key
LEFT JOIN fact_agent_intervals fai ON da.agent_key = fai.agent_key 
    AND dq.queue_key = fai.queue_key
    AND DATE(fc.start_call_time) = DATE(fai.interval_start)
WHERE NOT COALESCE(fc.is_abandoned, false)
GROUP BY 
    da.agent_name, 
    da.agent_role,
    da.workstation,
    da.operator_id,
    COALESCE(dq.queue_name, 'Unknown Queue'), 
    fc.client_code
ORDER BY da.agent_name, acd_group
```

---

## Report 5: Emergency Agent Performance

### Power BI Import Query
```sql
SELECT
    -- Agent Information
    da.agent_name,
    da.agent_role,
    da.workstation,
    
    -- Queue Information
    COALESCE(dq.queue_name, 'Unknown Queue') as acd_group,
    fc.client_code,
    
    -- Emergency Call Metrics
    COUNT(fc.call_key) as total_emergency_calls,
    COUNT(fc.call_key) / NULLIF(COUNT(DISTINCT DATE(fc.start_call_time)), 0) as avg_emergency_calls_per_shift,
    
    -- Time Metrics
    SUM(COALESCE(fai.available_time_seconds, 0)) / 3600.0 as available_time_hours,
    
    -- Emergency Service Level Metrics
    AVG(CASE WHEN fc.answered_within_10s AND fc.is_emergency THEN 100.0 ELSE 0.0 END) as emergency_answered_in_10s_percent,
    AVG(CASE WHEN fc.answered_within_15s AND fc.is_emergency THEN 100.0 ELSE 0.0 END) as emergency_answered_in_15s_percent,
    AVG(CASE WHEN fc.answered_within_20s AND fc.is_emergency THEN 100.0 ELSE 0.0 END) as emergency_answered_in_20s_percent,
    AVG(CASE WHEN fc.answered_within_40s AND fc.is_emergency THEN 100.0 ELSE 0.0 END) as emergency_answered_in_40s_percent,
    AVG(CASE WHEN NOT COALESCE(fc.answered_within_40s, false) AND fc.is_emergency THEN 100.0 ELSE 0.0 END) as emergency_answered_greater_40s_percent,
    
    -- Emergency Call Statistics
    AVG(fc.agent_time_to_answer_seconds) as avg_emergency_answer_time_seconds,
    AVG(fc.total_call_time_seconds) as avg_emergency_call_duration_seconds,
    
    -- Date Range
    MIN(DATE(fc.start_call_time)) as first_emergency_call_date,
    MAX(DATE(fc.start_call_time)) as last_emergency_call_date,
    COUNT(DISTINCT DATE(fc.start_call_time)) as emergency_call_days
    
FROM dim_agents da
JOIN fact_calls fc ON da.agent_key = fc.agent_key
JOIN dim_queues dq ON fc.queue_key = dq.queue_key
LEFT JOIN fact_agent_intervals fai ON da.agent_key = fai.agent_key 
    AND dq.queue_key = fai.queue_key
    AND DATE(fc.start_call_time) = DATE(fai.interval_start)
WHERE fc.is_emergency = true 
    AND NOT COALESCE(fc.is_abandoned, false)
GROUP BY 
    da.agent_name, 
    da.agent_role,
    da.workstation,
    COALESCE(dq.queue_name, 'Unknown Queue'), 
    fc.client_code
ORDER BY da.agent_name, acd_group
```

---

## Power BI Refresh Configuration

### Materialized Views for Performance
```sql
-- Create materialized views for each report
CREATE MATERIALIZED VIEW mv_acd_detailed_calls AS
SELECT * FROM view_acd_detailed_calls;

CREATE MATERIALIZED VIEW mv_queue_summary AS  
SELECT * FROM view_queue_summary;

CREATE MATERIALIZED VIEW mv_call_taking_overview AS
SELECT * FROM view_call_taking_overview;

CREATE MATERIALIZED VIEW mv_agent_performance AS
SELECT * FROM view_agent_performance;

CREATE MATERIALIZED VIEW mv_emergency_agent_performance AS
SELECT * FROM view_emergency_agent_performance;
```

### Hourly Refresh Lambda Function
```python
import boto3
import psycopg2

def lambda_handler(event, context):
    """Refresh materialized views every hour"""
    
    # Redshift connection
    conn = psycopg2.connect(
        host=os.environ['REDSHIFT_HOST'],
        database=os.environ['REDSHIFT_DB'],
        user=os.environ['REDSHIFT_USER'],
        password=os.environ['REDSHIFT_PASSWORD'],
        port=5439
    )
    
    cursor = conn.cursor()
    
    # Refresh all materialized views
    views_to_refresh = [
        'mv_acd_detailed_calls',
        'mv_queue_summary', 
        'mv_call_taking_overview',
        'mv_agent_performance',
        'mv_emergency_agent_performance'
    ]
    
    for view in views_to_refresh:
        cursor.execute(f"REFRESH MATERIALIZED VIEW {view}")
        
    conn.commit()
    cursor.close()
    conn.close()
    
    return {"status": "success", "views_refreshed": len(views_to_refresh)}
```

### Power BI Dataset Configuration
- **Refresh Schedule**: Every hour (60-minute intervals)
- **Data Source**: Materialized views in Redshift
- **Connection Mode**: Import Mode
- **Refresh Duration**: 2-5 minutes typical
- **Business Hours**: 24/7 for emergency services
