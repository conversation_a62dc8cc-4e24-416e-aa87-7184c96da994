-- ACD/Agent Reporting Data Warehouse Schema
-- Each client gets a separate schema for data isolation

-- Dimension Tables
CREATE TABLE IF NOT EXISTS dim_agents (
    agent_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_id VARCHAR(100) NOT NULL,
    agent_role VARCHAR(200),
    tenant_group VARCHAR(200),
    operator_id VARCHAR(50),
    workstation VARCHAR(100),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DISTSTYLE KEY DISTKEY(agent_id);

CREATE TABLE IF NOT EXISTS dim_queues (
    queue_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    queue_id VARCHAR(100) NOT NULL,
    queue_name VARCHAR(200),
    queue_uri VARCHAR(500),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DISTSTYLE KEY DISTKEY(queue_id);

CREATE TABLE IF NOT EXISTS dim_time (
    time_key BIGINT PRIMARY KEY,
    full_date DATE,
    year_num INTEGER,
    month_num INTEGER,
    day_num INTEGER,
    hour_num INTEGER,
    minute_num INTEGER,
    interval_5min INTEGER, -- 5-minute interval (0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55)
    day_of_week INTEGER,
    day_name VARCHAR(20),
    month_name VARCHAR(20),
    quarter_num INTEGER,
    is_weekend BOOLEAN,
    is_business_hour BOOLEAN
) DISTSTYLE ALL;

-- Fact Tables
CREATE TABLE IF NOT EXISTS fact_agent_sessions (
    session_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    login_time_key BIGINT REFERENCES dim_time(time_key),
    logout_time_key BIGINT REFERENCES dim_time(time_key),
    login_timestamp TIMESTAMP,
    logout_timestamp TIMESTAMP,
    session_duration_seconds INTEGER,
    device_name VARCHAR(100),
    reason_code VARCHAR(100),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DISTSTYLE KEY DISTKEY(agent_key);

CREATE TABLE IF NOT EXISTS fact_agent_states (
    state_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    state_timestamp TIMESTAMP,
    state_type VARCHAR(50), -- Available, BusiedOut, Break, Training, etc.
    state_duration_seconds INTEGER,
    reason_code VARCHAR(100),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DISTSTYLE KEY DISTKEY(agent_key);

CREATE TABLE IF NOT EXISTS fact_acd_sessions (
    acd_session_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    login_time_key BIGINT REFERENCES dim_time(time_key),
    logout_time_key BIGINT REFERENCES dim_time(time_key),
    login_timestamp TIMESTAMP,
    logout_timestamp TIMESTAMP,
    session_duration_seconds INTEGER,
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DISTSTYLE KEY DISTKEY(agent_key);

CREATE TABLE IF NOT EXISTS fact_queue_metrics (
    queue_metric_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    metric_timestamp TIMESTAMP,
    calls_received INTEGER DEFAULT 0,
    calls_answered INTEGER DEFAULT 0,
    calls_abandoned INTEGER DEFAULT 0,
    calls_transferred_in INTEGER DEFAULT 0,
    calls_transferred_out INTEGER DEFAULT 0,
    calls_answered_within_10s INTEGER DEFAULT 0,
    calls_answered_within_15s INTEGER DEFAULT 0,
    calls_answered_within_20s INTEGER DEFAULT 0,
    calls_answered_within_40s INTEGER DEFAULT 0,
    total_talk_time_seconds INTEGER DEFAULT 0,
    total_wrap_time_seconds INTEGER DEFAULT 0,
    total_handle_time_seconds INTEGER DEFAULT 0,
    agents_logged_in INTEGER DEFAULT 0,
    service_level_10s DECIMAL(5,2),
    service_level_15s DECIMAL(5,2),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DISTSTYLE KEY DISTKEY(queue_key);

CREATE TABLE IF NOT EXISTS fact_agent_metrics (
    agent_metric_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    metric_timestamp TIMESTAMP,
    calls_answered INTEGER DEFAULT 0,
    calls_transferred_in INTEGER DEFAULT 0,
    calls_transferred_out INTEGER DEFAULT 0,
    total_staffed_time_seconds INTEGER DEFAULT 0,
    total_available_time_seconds INTEGER DEFAULT 0,
    total_talk_time_seconds INTEGER DEFAULT 0,
    total_wrap_time_seconds INTEGER DEFAULT 0,
    total_handle_time_seconds INTEGER DEFAULT 0,
    reason_code_time_seconds INTEGER DEFAULT 0,
    reason_code VARCHAR(100),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) DISTSTYLE KEY DISTKEY(agent_key);

-- 5-Minute Interval Tables for Real-time Processing
CREATE TABLE IF NOT EXISTS fact_queue_intervals (
    interval_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    interval_start TIMESTAMP,
    interval_end TIMESTAMP,
    calls_offered INTEGER DEFAULT 0,
    calls_answered INTEGER DEFAULT 0,
    calls_abandoned INTEGER DEFAULT 0,
    calls_transferred_in INTEGER DEFAULT 0,
    calls_transferred_out INTEGER DEFAULT 0,
    agents_logged_in INTEGER DEFAULT 0,
    agents_available INTEGER DEFAULT 0,
    agents_on_call INTEGER DEFAULT 0,
    agents_in_wrap INTEGER DEFAULT 0,
    agents_unavailable INTEGER DEFAULT 0,
    avg_answer_time_seconds DECIMAL(8,2),
    avg_abandon_time_seconds DECIMAL(8,2),
    avg_talk_time_seconds DECIMAL(8,2),
    avg_wrap_time_seconds DECIMAL(8,2),
    service_level_10s DECIMAL(5,2),
    service_level_15s DECIMAL(5,2),
    service_level_20s DECIMAL(5,2),
    service_level_40s DECIMAL(5,2),
    longest_waiting_call_seconds INTEGER DEFAULT 0,
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(queue_key, interval_start)
) DISTSTYLE KEY DISTKEY(queue_key);

CREATE TABLE IF NOT EXISTS fact_agent_intervals (
    agent_interval_key BIGINT IDENTITY(1,1) PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    time_key BIGINT REFERENCES dim_time(time_key),
    interval_start TIMESTAMP,
    interval_end TIMESTAMP,
    calls_answered INTEGER DEFAULT 0,
    calls_transferred_in INTEGER DEFAULT 0,
    calls_transferred_out INTEGER DEFAULT 0,
    calls_on_hold INTEGER DEFAULT 0,
    staffed_time_seconds INTEGER DEFAULT 0,
    available_time_seconds INTEGER DEFAULT 0,
    talk_time_seconds INTEGER DEFAULT 0,
    wrap_time_seconds INTEGER DEFAULT 0,
    hold_time_seconds INTEGER DEFAULT 0,
    unavailable_time_seconds INTEGER DEFAULT 0,
    primary_reason_code VARCHAR(100),
    utilization_percentage DECIMAL(5,2),
    occupancy_percentage DECIMAL(5,2),
    client_code VARCHAR(20) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_key, queue_key, interval_start)
) DISTSTYLE KEY DISTKEY(agent_key);

-- Real-time Status Tables
CREATE TABLE IF NOT EXISTS real_time_queue_status (
    queue_key BIGINT REFERENCES dim_queues(queue_key),
    last_updated TIMESTAMP,
    current_calls_waiting INTEGER DEFAULT 0,
    current_agents_logged_in INTEGER DEFAULT 0,
    current_agents_available INTEGER DEFAULT 0,
    current_agents_on_call INTEGER DEFAULT 0,
    longest_waiting_seconds INTEGER DEFAULT 0,
    current_service_level DECIMAL(5,2),
    calls_answered_last_5min INTEGER DEFAULT 0,
    calls_abandoned_last_5min INTEGER DEFAULT 0,
    avg_answer_time_last_5min DECIMAL(8,2),
    client_code VARCHAR(20) NOT NULL,
    PRIMARY KEY (queue_key, last_updated)
) DISTSTYLE KEY DISTKEY(queue_key);

CREATE TABLE IF NOT EXISTS real_time_agent_status (
    agent_key BIGINT REFERENCES dim_agents(agent_key),
    last_updated TIMESTAMP,
    current_state VARCHAR(50),
    state_start_time TIMESTAMP,
    state_duration_seconds INTEGER,
    current_queue VARCHAR(100),
    current_call_id VARCHAR(100),
    is_available BOOLEAN DEFAULT false,
    reason_code VARCHAR(100),
    last_call_end_time TIMESTAMP,
    client_code VARCHAR(20) NOT NULL,
    PRIMARY KEY (agent_key, last_updated)
) DISTSTYLE KEY DISTKEY(agent_key);

-- Aggregated Tables for Performance
CREATE TABLE IF NOT EXISTS agg_hourly_queue_metrics (
    queue_key BIGINT,
    hour_timestamp TIMESTAMP,
    calls_received INTEGER,
    calls_answered INTEGER,
    calls_abandoned INTEGER,
    service_level_10s DECIMAL(5,2),
    avg_agents_logged_in DECIMAL(5,2),
    total_talk_time_seconds INTEGER,
    total_wrap_time_seconds INTEGER,
    client_code VARCHAR(20),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (queue_key, hour_timestamp)
) DISTSTYLE KEY DISTKEY(queue_key);

CREATE TABLE IF NOT EXISTS agg_daily_agent_metrics (
    agent_key BIGINT,
    date_key DATE,
    total_staffed_time_seconds INTEGER,
    total_available_time_seconds INTEGER,
    total_calls_answered INTEGER,
    total_talk_time_seconds INTEGER,
    total_wrap_time_seconds INTEGER,
    utilization_percentage DECIMAL(5,2),
    client_code VARCHAR(20),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (agent_key, date_key)
) DISTSTYLE KEY DISTKEY(agent_key);

-- Indexes for performance
CREATE INDEX idx_fact_agent_sessions_timestamp ON fact_agent_sessions(login_timestamp);
CREATE INDEX idx_fact_agent_states_timestamp ON fact_agent_states(state_timestamp);
CREATE INDEX idx_fact_queue_metrics_timestamp ON fact_queue_metrics(metric_timestamp);
CREATE INDEX idx_fact_agent_metrics_timestamp ON fact_agent_metrics(metric_timestamp);
