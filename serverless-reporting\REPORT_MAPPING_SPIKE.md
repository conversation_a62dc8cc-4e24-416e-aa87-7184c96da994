# Agent Reports Data Analysis & Field Mapping Spike

## Scope & Outcomes - Agent Reports Only

**Out of scope**: Call-level metrics (entered/answered/SLA by queue). Those stay in the Call Summary spike.

**In scope**: Everything that depends on Agent/ACD events from i3 logs, namely:

- **Staffed time** (Logged-In)
- **Available time**
- **Clerical / Wrap-up time**
- **Other not-ready time** (Break/Training/...)
- **Talk time** (credited to agent; source is callsummary talk windows)
- **Queue calls answered** (agent)
- **Transfers in/out** (agent)
- **Average agents logged in**
- **Skills/roles lists and counts**
- **Agent↔Queue participation** (which queues the agent was logged/active in)
- **Per-agent service levels** (depends on callsummary flags, but scoped to agent)

**Canonical output grain**: `(tenant, ring_group, agent, date, 5-min bucket)`

## i3 Events & Fields Required

### Event Types (Minimal Set)

| Category | i3 Event(s) | Used for... |
|----------|-------------|-------------|
| Agent Presence | Login, Logout, AgentAvailable, AgentBusiedOut | Build staffed/available/wrap-up/other spans |
| Queue Presence | ACDLogin, ACDLogout | Bind agent ↔ queue (ring group) |
| (From CallSummary) | external | Talk windows; answered counts; SLA flags |

### Fields Required (from i3 XML)

| XML Field | Present in | Why we need it / rules |
|-----------|------------|------------------------|
| timestamp | all | Span boundaries; bucket assignment (UTC) |
| mediaLabel | all | Query agent/ACD events |
| tenantGroup | Login, ACDLogin, ... | Tenant key; also for grouped views |
| agent | Presence & ACD events | Agent natural key |
| operatorId | TBD | TBD |
| workstation | Presence | Optional analytics |
| uri/agentUri | Presence | Optional: match transfer targets |
| deviceName | Login/Logout | Rule: only treat deviceName='Headset' as staffing boundaries |
| agentRole | Presence | Feed skills/role statistics |
| busiedOutAction | AgentBusiedOut | Classify reason: WrapUp vs other |
| ringGroupName | ACDLogin/Logout | Queue (ring group) natural key |
| ringGroupUri | ACDLogin/Logout | Disambiguate same names across tenants |

### Fields Required (from callsummary, as inputs to agent metrics)

| Field (external) | Used for... |
|------------------|-------------|
| callIdentifier | Join key when crediting talk/answers to agent |
| agentName | The answering agent for the call |
| startCallTime, endCallTime | Talk window anchoring (together with talkTimeInSeconds) |
| talkTimeInSeconds | Talk seconds credited to the agent |
| agentAnsweredWithinXXs | Agent-level SLA (%≤10s, ≤15s, etc.) |
| transferFrom/transferTo | Agent-level transfers in/out |

## Agent State Model

The goal is to build non-overlapping spans per `(tenant, agent, ring_group)` with these states:

- **LOGGED_IN** - bounded by ACDLogin(Headset) → ACDLogout(Headset)
- **AVAILABLE** - AgentAvailable → next of {AgentBusiedOut, LOGGED_OUT, ONCALL_START}
- **BUSY_WRAPUP** - from AgentBusiedOut when busiedOutAction IN {WrapUpFixedDuration, WrapUpVariableDuration} → next availability/other
- **BUSY_OTHER** - from AgentBusiedOut with any other action (Break/Training/Meeting/Manual Busy...)
- **ONCALL** - talk window per answered call (source: callsummary; credited to agent's active ring group)
- **LOGGED_OUT** - outside any staffed window

## Pseudo-code: From Events to Agent Metrics

### 1. Stitch Staffed Sessions
```
for each (tenantGroup, agent, ringGroup):
  L = ordered ACDLogin events where deviceName='Headset'
  O = ordered ACDLogout events where deviceName='Headset'
  pair in order:
     session = [login.ts, logout.ts)  -- if missing logout, close at now (soft close)
     emit SPAN(STATE=LOGGED_IN, start, end, tenant, agent, ringGroup)
```

### 2. Within Each Staffed Session, Build Availability & Busy Spans
```
for each staffed SPAN S:
  cursor = S.start
  for e in events between S.start..S.end ordered by timestamp:
    if e.type == AgentAvailable:
        close any open AVAILABLE/BUSY spans at e.ts
        open AVAILABLE from e.ts
    if e.type == AgentBusiedOut:
        close AVAILABLE at e.ts
        if e.busiedOutAction startswith 'WrapUp':
            open BUSY_WRAPUP from e.ts
        else:
            open BUSY_OTHER from e.ts
    if e.type in {Logout, ACDLogout}:
        close all at e.ts
  close any open at S.end
```

### 3. Add ONCALL (Talk) Spans from CallSummary
```
for each answered call c where c.agentName = agent:
  talk_dur = coalesce(c.talkTimeInSeconds, max(0, c.endCallTime - c.startCallTime))
  talk = [c.startCallTime, c.startCallTime + talk_dur)
  rg = resolve_agent_ring_group_at(c.startCallTime)  -- from ACDLogin/queue presence nearest <= start
  emit SPAN(STATE=ONCALL, start=talk.start, end=talk.end, tenant, agent, ringGroup=rg)
```

### 4. Normalize (Clip/Merge) Spans with State Precedence
```
-- Build a timeline of atomic sub-intervals bounded by all start/end points in STAFFED+AVAIL+BUSY+ONCALL
timeline = cut_into_atomic_intervals(all_spans)
for each interval I in timeline:
  states = states_active_over(I)
  chosen = highest_precedence(states)  -- ONCALL > WRAPUP > AVAILABLE > BUSY_OTHER > LOGGED_IN
  emit NORMALIZED_SPAN(chosen, I.start, I.end, tenant, agent, ringGroup)
```

### 5. Bucket to 5-minute and Accumulate Seconds
```
for each NORMALIZED_SPAN NS:
    for each 5_min_bucket B that overlaps NS:
        secs = seconds_overlap(NS, B)          # length of the overlap in seconds
        AGG[tenant, ringGroup, agent, B].seconds[NS.state] += secs

        if NS.state == LOGGED_IN:
            AGG[tenant, ringGroup, agent, B].agents_logged_in_seconds += secs
```

### 6. Count Agent Answers & Transfers (Optional at Agent Level)
```
for each answered call c:
  B = bucket_5min(c.startCallTime)
  AGG[tenant, rg_of(c), agentName, B].queue_calls_answered += 1

for each call c with transferFrom/transferTo:
  if transfer initiated by agent A at ts:
      B = bucket_5min(ts); AGG[tenant, rg_of(A,ts), A, B].transferred_out += 1
  if target agent/queue matches A's queue at ts:
      AGG[...].transferred_in += 1
```

## Resulting Per-Bucket Fields

For each `(tenant, ring_group, agent, date, time5)`:

- **staffed_time_seconds** = seconds[LOGGED_IN] + seconds[AVAILABLE] + seconds[BUSY_WRAPUP] + seconds[BUSY_OTHER] + seconds[ONCALL]
- **available_time_seconds** = seconds[AVAILABLE]
- **wrapup_time_seconds** = seconds[BUSY_WRAPUP]
- **busy_other_time_seconds** = seconds[BUSY_OTHER]
- **talk_time_seconds** = seconds[ONCALL] (must match callsummary talk)
- **queue_calls_answered** (count)
- **transferred_in, transferred_out** (optional)
- **agents_logged_in_seconds** (for avg headcount)

## Metrics For Agent Reports

### Total Staffed Time
- **Formula**: `sum(staffed_time_seconds)`
- **Inputs**: ACDLogin/ACDLogout (Headset), derived spans

### Total Available Time
- **Formula**: `sum(available_time_seconds)`
- **Inputs**: AgentAvailable spans (clipped by staffed)

### Total Clerical (Wrap-up) Time
- **Formula**: `sum(wrapup_time_seconds)`
- **Inputs**: AgentBusiedOut(busiedOutAction startswith 'WrapUp')

### Other Not-Ready Time
- **Formula**: `sum(busy_other_time_seconds)`
- **Inputs**: AgentBusiedOut (non-wrap-up actions)

### Total Conversation (Talk) Time
- **Formula**: `sum(talk_time_seconds)`
- **Inputs**: callsummary talk windows (credited to agent)

### Queue Calls Answered (Agent)
- **Formula**: `sum(queue_calls_answered)`
- **Inputs**: callsummary answered calls where agentName=agent

### Transfers In/Out (Agent) (Optional)
- **Formula**: `sum(transferred_in)`, `sum(transferred_out)`
- **Inputs**: transfer initiation/target data (from callsummary transferFrom/To or separate events)

### Average Call Holding (Conversation) Time
- **Formula**: `sum(talk_time_seconds) / nullif(sum(queue_calls_answered),0)`

### Average Handling Time (Talk + Wrap-up)
- **Formula**: `(sum(talk_time_seconds) + sum(wrapup_time_seconds)) / nullif(sum(queue_calls_answered),0)`

### Average # Agents Logged In
- **Formula**: `sum(agents_logged_in_seconds) / 300.0` (per bucket)
- **For longer ranges**: time-weighted average = average over buckets

### Skills Set Statistics
- **Formula**: Group the above sums by agentRole → counts, totals, averages

### Queues Agent Was Logged/Active In
- **Formula**: `distinct ring_group where staffed_time_seconds > 0 in period`

### Per-Agent Service Level (%≤10s, etc.) (Depends on CallSummary)
- **Formula**: `%≤10s = sum(1 for answered calls with agentAnsweredWithin10s=1) / sum(1 for answered)`
- **Inputs**: callsummary flags per call; group by agent

### Trunk/Route to Which Connected (Enrichment)
- **Status**: TBD (what's trunk?)

## Example (What a Bucket Contains)

For `(tenant=T, ring_group=Police CT, agent=alice, date=2025-02-05, time5=10:05)`:

```
staffed_time_seconds=300
available_time_seconds=120
wrapup_time_seconds=30
busy_other_time_seconds=0
talk_time_seconds=150
queue_calls_answered=2
transferred_in=0
transferred_out=1
agents_logged_in_seconds=300
```

From these, dashboards compute utilization, avg handling, SLA by agent, etc.

## Data Quality & Edge Cases (Agent Side)

1. **Headset-only staffing**: ignore Login/Logout not on deviceName='Headset'
2. **Missing logout**: close staffed session at now (soft close); fix later if logout arrives
3. **Overlaps**: resolve by precedence (ONCALL > WRAPUP > AVAILABLE > BUSY_OTHER > LOGGED_IN)
4. **Wrap-up without action**: if some environments don't emit explicit WrapUp, you may infer wrap-up as (endCall → next Available); mark as inferred

## Story Seeds (What This Spike Drives)

- **S1**: Parse & normalize presence and ACD login/logout events (fields above)
- **S2**: Build staffed sessions and availability/busy/wrap-up spans with precedence
- **S3**: Credit talk to agents by joining callsummary; emit ONCALL spans
- **S4**: Bucket spans to 5-minute intervals; compute per-bucket seconds and counters
- **S5**: Publish Agent dataset (per-bucket fact + conformed dims)

## ER Diagram Mapping to Agent Reports

The updated ER diagram supports all agent-focused metrics through these key tables:

### Core Tables for Agent Metrics

| Table | Purpose | Key Fields |
|-------|---------|------------|
| `fact_agent_intervals` | Pre-aggregated 5-min buckets | staffed_time_seconds, available_time_seconds, wrapup_time_seconds, talk_time_seconds, calls_answered |
| `fact_agent_state` | Raw agent state spans | state_type, state_duration_sec, busied_out_action |
| `fact_agent_session` | Login/logout sessions | session_duration_sec, device_name |
| `fact_call` | Call details for agent credit | talk_time_sec, agent_id, answered_le_*s_flag |
| `dim_agent` | Agent attributes | agent_name, agent_role |
| `dim_queue` | Queue/ring group mapping | queue_name, ring_group_name |

### Power BI Query Example (Agent Performance)
```sql
SELECT
    da.agent_name,
    dq.queue_name as ring_group,
    SUM(fai.staffed_time_seconds) / 3600.0 as total_staffed_hours,
    SUM(fai.available_time_seconds) / 3600.0 as total_available_hours,
    SUM(fai.wrapup_time_seconds) / 3600.0 as total_wrapup_hours,
    SUM(fai.talk_time_seconds) / 3600.0 as total_talk_hours,
    SUM(fai.calls_answered) as total_calls_answered,
    AVG(fai.service_level_10s) as avg_service_level_10s
FROM fact_agent_intervals fai
JOIN dim_agent da ON fai.agent_id = da.agent_id
JOIN dim_queue dq ON fai.queue_id = dq.queue_id
WHERE fai.tenant_id = @tenant_id
    AND fai.interval_start >= @start_date
    AND fai.interval_start < @end_date
GROUP BY da.agent_name, dq.queue_name
```

## Implementation Notes

1. **No Roster/Schedule Tables**: Removed as requested - focus purely on actual time worked
2. **5-Minute Buckets**: `fact_agent_intervals` provides pre-aggregated data for performance
3. **State Precedence**: ONCALL > WRAPUP > AVAILABLE > BUSY_OTHER > LOGGED_IN
4. **Multi-Tenant**: All tables include tenant_id for client isolation
5. **Historical Tracking**: Dimension tables support SCD Type 2 for agent/queue changes

The ER diagram now fully supports all agent report requirements without roster/schedule complexity.
```