# SD2 Serverless Data Warehouse - Comprehensive Architecture Spike

## Executive Summary

This spike documents the complete **SD2 (Serverless Data 2.0)** architecture for ACD/Agent reporting using AWS serverless technologies. The system processes both call data and ACD events to populate a star schema data warehouse optimized for Power BI reporting.

## Architecture Overview

### High-Level Data Flow
```
S3 Bucket → SNS → Lambda Router → Dual Processing Paths:
├── Call Events → i3Event Handler → Collector API → Redshift (raw_callsummary)
└── ACD Events → ACD Processor → Redshift (agent fact tables)
                                      ↓
EventBridge Schedule → Data Transformer → Redshift (star schema transformation)
```

### Key Components
1. **Event Router Lambda**: Routes events by type (Call vs ACD)
2. **i3Event Handler Lambda**: Existing call processing (unchanged)
3. **Collector API**: Existing containerized app with dual-write capability
4. **ACD Processor Lambda**: NEW - processes agent/ACD events
5. **Data Transformer Lambda**: NEW - transforms raw data into star schema
6. **Redshift Serverless**: Data warehouse with optimized star schema

## Star Schema Design Analysis

### ✅ Optimized 15-Minute Interval Design
The proposed star schema is **excellent** for SD2 requirements:

**Dimensions (7 tables):**
- `dim_tenant` - Multi-client isolation
- `dim_agent` - Agent master with SCD Type 2
- `dim_queue` - Queue master with SCD Type 2  
- `dim_date` - Date dimension
- `dim_time15` - 15-minute time buckets (96 per day)
- `dim_call_type` - Emergency/Admin/Outbound classification
- `dim_reason_code` - Agent unavailability reasons

**Facts (5 tables):**
- `fact_call` - Individual call records
- `fact_agent_session` - Login/logout sessions
- `fact_agent_state` - State changes (Available/Busy/Wrap-up)
- `fact_agent_interval15` - Pre-aggregated 15-min agent metrics
- `fact_queue_interval15` - Pre-aggregated 15-min queue metrics

### Performance Benefits
- **15-minute intervals**: Dashboard queries hit pre-aggregated data
- **Surrogate keys**: Fast integer joins vs string comparisons
- **Multi-tenant**: Complete client isolation via tenant_key
- **SCD Type 2**: Historical accuracy for agent role changes

## Data Processing Flows

### Flow 1: Call Data Processing (Existing + Enhanced)
```
S3 → SNS → Event Router → i3Event Handler → Collector API → MariaDB + Redshift
```

**Data Elements:**
- Call identifier, agent name, queue name (tenant_psap_name)
- Talk time, total time, service level flags
- Transfer information, emergency/admin flags
- Pre-calculated service level booleans

**Collector API Changes:**
```csharp
// Add dual-write capability
await WriteToMariaDB(callSummary);        // Existing
await WriteToRedshift(callSummary);       // NEW
```

### Flow 2: ACD Event Processing (New)
```
S3 → SNS → Event Router → ACD Processor → Redshift (agent tables)
```

**ACD Event Types Processed:**
- **Login/Logout**: Agent session tracking
- **AgentAvailable/AgentBusiedOut**: State change tracking  
- **ACDLogin/ACDLogout**: Queue membership tracking
- **QueueStateChange**: Queue monitoring (optional)

**Key Data Extracted:**
- Agent: name, role, tenant group, operator ID, workstation
- Session: login/logout times, queue associations
- States: Available, Busy, Wrap-up, Break, Training
- Reason codes: Manual, Break, Training, WrapUpFixedDuration

### Flow 3: Data Transformation (New)
```
EventBridge (5-min schedule) → Data Transformer → Redshift (star schema)
```

**Transformation Steps:**
1. **Dimension Updates**: Upsert agent/queue dimensions with SCD Type 2
2. **Call Facts**: Transform raw_callsummary → fact_call
3. **Session Facts**: Process agent sessions → fact_agent_session  
4. **State Facts**: Process state changes → fact_agent_state
5. **Interval Aggregation**: Calculate 15-min metrics → fact_agent_interval15, fact_queue_interval15

## SD2 Key Concepts

### What is SD2 (Serverless Data 2.0)?
- **Serverless-first**: Lambda + Redshift Serverless for elastic scaling
- **Event-driven**: Real-time processing of streaming data
- **Multi-tenant**: Complete client isolation at data and infrastructure level
- **Cost-optimized**: Pay-per-use model vs always-on infrastructure
- **Near real-time**: 5-minute transformation cycles for dashboard updates

### Multi-Tenant Architecture
```
Client A: S3 bucket → SNS topic → Lambda → Redshift namespace
Client B: S3 bucket → SNS topic → Lambda → Redshift namespace  
Client C: S3 bucket → SNS topic → Lambda → Redshift namespace
```

**Isolation Levels:**
- **Infrastructure**: Separate S3 buckets, SNS topics, Lambda functions
- **Data**: Separate Redshift namespaces + tenant_key filtering
- **Processing**: Client-specific Lambda environment variables

### Performance Optimization
- **Pre-aggregation**: 15-minute interval facts eliminate real-time calculations
- **Partitioning**: Redshift tables partitioned by date_key
- **Compression**: Optimized data types (smallint for counts, boolean for flags)
- **Indexing**: Surrogate keys for fast joins

## Critical Calculations

### Group Utilization Formula
```sql
-- Report 2 & 3: Group Utilization %
(SUM(fact_agent_interval15.talk_sec) / SUM(fact_agent_interval15.staffed_sec)) * 100

-- Where:
-- talk_sec = Total time agents spent talking on calls
-- staffed_sec = Total time agents were logged in and available
```

### Service Level Calculations
```sql
-- Service Level % (10s, 15s, 20s, 40s thresholds)
AVG(CASE WHEN fact_call.answered_le_10s THEN 100.0 ELSE 0.0 END)

-- Uses pre-calculated boolean flags from MariaDB callsummary
```

### Staffed Time vs Available Time
```sql
-- Staffed Time = Total login duration
SUM(fact_agent_session.session_sec) / 3600  -- Convert to hours

-- Available Time = Time in "Available" state  
SUM(fact_agent_state.state_sec WHERE state_type = 'Available') / 3600
```

### Agent State Duration Calculation
```sql
-- State duration = Next state timestamp - Current state timestamp
-- Handled by ACD Processor Lambda using event sequencing
```

## Report Query Examples

### Report 1: ACD - Detailed Calls by Group
```sql
SELECT
    q.queue_name as acdRingGroup,
    SUM(ai.calls_answered) as callsAnswered,
    SUM(ai.transferred_in) as transferredIn,
    SUM(ai.transferred_out) as transferredOut,
    SUM(ai.staffed_sec)/3600 as loggedInTime,
    SUM(ai.available_sec)/3600 as availableTime,
    SUM(ai.wrapup_sec)/60 as wrapupTime,
    SUM(ai.talk_sec)/3600 as talkTime,
    STRING_AGG(DISTINCT a.agent_name) as agentsLoggedIn
FROM fact_agent_interval15 ai
JOIN dim_queue q ON ai.queue_key = q.queue_key
JOIN dim_agent a ON ai.agent_key = a.agent_key
JOIN dim_date d ON ai.date_key = d.date_key
WHERE d.full_date = '2024-01-15'
  AND ai.tenant_key = @tenant_key
GROUP BY q.queue_name;
```

### Report 2: Call Queue Summary Report
```sql
SELECT
    q.queue_name,
    SUM(qi.calls_entered) as calls,
    AVG(qi.answered_le_10s * 100.0 / NULLIF(qi.calls_entered,0)) as callsAnsweredIn10sPercent,
    SUM(qi.abandoned_calls) as callsAbandoned,
    SUM(ai.staffed_sec)/3600 as loggedInTime,
    COUNT(DISTINCT ai.agent_key) as agentsLoggedIn,
    (SUM(ai.talk_sec) / NULLIF(SUM(ai.staffed_sec),0)) * 100 as groupUtilization
FROM fact_queue_interval15 qi
JOIN fact_agent_interval15 ai ON qi.queue_key = ai.queue_key
    AND qi.date_key = ai.date_key
    AND qi.time15_key = ai.time15_key
JOIN dim_queue q ON qi.queue_key = q.queue_key
JOIN dim_date d ON qi.date_key = d.date_key
WHERE d.full_date = '2024-01-15'
  AND qi.tenant_key = @tenant_key
GROUP BY q.queue_name;
```

### Report 3: Call Taking Group Overview
```sql
SELECT
    q.queue_name as acdGroup,
    COUNT(fc.call_fact_id) as calls,
    SUM(CASE WHEN fc.answered_le_10s THEN 1 ELSE 0 END) as callsAnsweredWithin10s,
    SUM(CASE WHEN fc.answered_le_15s THEN 1 ELSE 0 END) as callsAnsweredWithin15s,
    SUM(CASE WHEN fc.answered_le_20s THEN 1 ELSE 0 END) as callsAnsweredWithin20s,
    SUM(CASE WHEN fc.answered_le_40s THEN 1 ELSE 0 END) as callsAnsweredWithin40s,
    SUM(CASE WHEN NOT fc.answered_le_40s AND NOT fc.abandoned_flag THEN 1 ELSE 0 END) as callsAnsweredAbove40s,
    SUM(CASE WHEN fc.abandoned_flag THEN 1 ELSE 0 END) as callsAbandoned,
    SUM(CASE WHEN fc.transferred_flag THEN 1 ELSE 0 END) as callsTransferred,
    AVG(CASE WHEN fc.answered_le_10s THEN 100.0 ELSE 0.0 END) as callsAnsweredWithin10sPercent,
    AVG(CASE WHEN fc.answered_le_15s THEN 100.0 ELSE 0.0 END) as callsAnsweredWithin15sPercent,
    SUM(ais.staffed_sec)/60 as loggedInTime,
    SUM(ais.available_sec)/60 as availableTime,
    (SUM(ais.available_sec) / NULLIF(SUM(ais.staffed_sec),0)) * 100 as groupUtilizationPercent
FROM fact_call fc
JOIN dim_queue q ON fc.queue_key = q.queue_key
JOIN dim_date d ON fc.start_date_key = d.date_key
LEFT JOIN (
    SELECT queue_key, SUM(staffed_sec) as staffed_sec, SUM(available_sec) as available_sec
    FROM fact_agent_interval15
    WHERE date_key = (SELECT date_key FROM dim_date WHERE full_date = '2024-01-15')
    GROUP BY queue_key
) ais ON fc.queue_key = ais.queue_key
WHERE d.full_date = '2024-01-15'
  AND fc.tenant_key = @tenant_key
GROUP BY q.queue_name;
```

## Implementation Roadmap

### Phase 1: Infrastructure Setup (Week 1)
1. **Redshift Serverless**: Create namespace with star schema tables
2. **Lambda Functions**: Deploy Event Router and ACD Processor
3. **SNS/SQS**: Configure event routing for ACD events
4. **IAM Roles**: Set up proper permissions for cross-service access

### Phase 2: ACD Processing (Week 2)
1. **ACD Processor Lambda**: Implement agent session and state tracking
2. **Dimension Management**: Build SCD Type 2 logic for agents/queues
3. **Event Sequencing**: Handle out-of-order events and state transitions
4. **Testing**: Validate ACD event processing with sample data

### Phase 3: Call Data Integration (Week 3)
1. **Collector API Enhancement**: Add dual-write to Redshift
2. **Data Transformer**: Build transformation logic for star schema
3. **Interval Aggregation**: Implement 15-minute pre-aggregation
4. **Scheduling**: Set up EventBridge for regular transformations

### Phase 4: Reporting & Optimization (Week 4)
1. **Power BI Views**: Create optimized views for each report
2. **Performance Tuning**: Add indexes, optimize queries
3. **Monitoring**: Set up CloudWatch dashboards and alerts
4. **Documentation**: Complete operational runbooks

## Architecture Validation

### ✅ Your High-Level Diagram Analysis
Your diagram **perfectly supports** the star schema requirements:

1. **Event Router**: ✅ Routes Call vs ACD events correctly
2. **i3Event Handler**: ✅ Handles call processing (unchanged)
3. **ACD Processor**: ✅ Processes agent events for fact tables
4. **Data Transformer**: ✅ Transforms raw data into star schema
5. **Redshift**: ✅ Stores optimized star schema for reporting

### Required Enhancements
1. **Collector API**: Add dual-write capability (MariaDB + Redshift)
2. **Event Bridge**: Add scheduled transformation trigger
3. **Multi-tenant**: Ensure tenant_key propagation throughout

## Success Metrics

### Performance Targets
- **Event Processing**: < 1 second per ACD event
- **Dashboard Refresh**: < 5 minutes for near real-time updates
- **Report Queries**: < 10 seconds for daily/weekly reports
- **Scalability**: Handle 100K+ events per hour per client

### Cost Optimization
- **Redshift Serverless**: Auto-scaling based on query load
- **Lambda**: Pay-per-execution model
- **S3**: Lifecycle policies for log retention
- **Multi-tenant**: Shared infrastructure with data isolation
