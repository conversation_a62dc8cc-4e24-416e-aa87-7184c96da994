# ACD/Agent Reporting System - Technical Spike (FINAL)

## Executive Summary

This spike documents the **final V3 architecture** for ACD/Agent reporting that leverages existing infrastructure while adding minimal complexity. The **Collector API is minimally modified** to write call summaries directly to both MariaDB and Redshift, eliminating DMS/CDC complexity. ACD/Agent events are processed via new serverless architecture for real-time metrics.

## Architecture Overview (FINAL)

### Three-Pipeline Architecture

**Pipeline 1: Call Data Processing (EXISTING + MINIMAL MODIFICATION)**
```
S3 → SNS → SQS → Lambda → Collector API (EC2) → MariaDB + Redshift raw_callsummary
```
- **KEEP EXISTING**: S3 → SNS → SQS → Lambda infrastructure (no changes)
- **MINIMAL CHANGE**: Collector API writes to both MariaDB and Redshift `raw_callsummary`
- Handles StartCall, EndCall, CallAnswered events as it currently does
- **NO NEW ROUTER LAMBDA**: Use existing event routing infrastructure

**Pipeline 2: ACD/Agent Processing (NEW - Serverless)**
```
S3 → SNS → Lambda (ACD Processor) → Redshift agent tables
```
- **NEW**: Direct SNS subscription for ACD events (Login, Logout, AgentAvailable, ACDLogin, etc.)
- Processes real-time agent state changes and sessions
- Writes to fact_agent_sessions, fact_acd_sessions, fact_agent_states, real_time_agent_status

**Pipeline 3: Data Transformation (NEW - Scheduled)**
```
CloudWatch Events → Lambda (Data Transformer) → Redshift fact_calls + dimensions
```
- Scheduled Lambda runs every 5-15 minutes
- Transforms raw_callsummary into star schema fact tables via stored procedures
- Creates/updates agent and queue dimensions from call data
- Calculates 5-minute interval aggregations for real-time dashboards
```
## Detailed Data Flow

### Step 1: Event Ingestion (Leverage Existing Infrastructure)

**All Events Start the Same:**
```
i3logs XML Files → S3 Bucket → S3 Event Notification → SNS Topic
```

### Step 2: Event Processing (Two Paths)

**Call Events (EXISTING INFRASTRUCTURE + MINIMAL CHANGE):**
```
SNS → SQS → Lambda → Collector API (EC2) → MariaDB + Redshift raw_callsummary
```
- **UNCHANGED**: StartCall, EndCall, CallAnswered events continue through existing infrastructure
- **MINIMAL CHANGE**: Collector API writes to both MariaDB and Redshift simultaneously
- **SAME DATA**: Redshift `raw_callsummary` has identical structure to MariaDB table

**ACD/Agent Events (NEW SERVERLESS PIPELINE):**
```
SNS → Lambda (ACD Processor) → Redshift agent tables
```
- **NEW**: Direct SNS subscription for Login, Logout, AgentAvailable, ACDLogin, ACDLogout, QueueStateChange
- **REAL-TIME**: Immediate processing for agent state tracking
- **WRITES TO**: fact_agent_sessions, fact_acd_sessions, fact_agent_states, real_time_agent_status

### Step 3: Data Transformation (NEW SCHEDULED PIPELINE)

**Scheduled Data Processing:**
```
CloudWatch Events (every 5-15 min) → Lambda (Data Transformer) → Stored Procedures
```
1. **Raw to Facts**: Transforms `raw_callsummary` → `fact_calls` via `sp_transform_callsummary_to_facts()`
2. **Dimension Updates**: Creates/updates `dim_agents`, `dim_queues` from call data
3. **Interval Calculations**: Generates 5-minute aggregations for dashboards
4. **Data Quality**: Handles missing agent sessions, queue associations

### Step 4: Data Association Strategy

**Linking Call Data with Agent Data:**
- **Call Data Source**: `raw_callsummary.agent_name` field (from Collector API)
- **Agent Data Source**: ACD events with agent identifiers
- **Association Method**:
  1. Create unified `dim_agents` from both call data and ACD events
  2. Match agents by name/ID across both pipelines
  3. Use time-based correlation (call timestamp overlaps with agent session)
  4. Handle missing agent sessions gracefully with LEFT JOINs in Power BI views

## Report Requirements Mapping

### Report 1: ACD - Detailed Calls by Group (CORRECTED)

| RFP Requirement | Data Source | Calculation Method |
|----------------|-------------|-------------------|
| **Ring Group** | `fact_calls_from_summary.tenant_psap_name` OR `dim_queues.queue_name` | Direct from callsummary or queue dimension |
| **Calls Answered** | `fact_calls_from_summary` | `COUNT(*)` WHERE NOT `is_abandoned` |
| **Transferred In** | `fact_calls_from_summary.transfer_to` | `SUM(CASE WHEN is_transferred AND transfer_to IS NOT NULL)` |
| **Transferred Out** | `fact_calls_from_summary.transfer_from` | `SUM(CASE WHEN is_transferred AND transfer_from IS NOT NULL)` |
| **Talk Time** | `fact_calls_from_summary.total_call_time_seconds - hold_time_seconds` | Calculated from MariaDB callsummary |
| **Wrap-up Time** | `fact_agent_intervals.wrap_time_seconds` | Aggregated from ACD agent state events |
| **Handling Time** | `fact_calls_from_summary.total_call_time_seconds` | Direct from MariaDB callsummary |
| **Staffed Time** | `fact_agent_intervals.staffed_time_seconds` | Aggregated from ACD login/logout events |
| **Available Time** | `fact_agent_intervals.available_time_seconds` | Aggregated from agent state events |
| **Service Level** | `fact_calls_from_summary.agent_answered_within_10s` | Pre-calculated in MariaDB callsummary |
| **Agents Logged In** | `fact_calls_from_summary.agent_name` + `fact_acd_sessions` | Combined from call data and ACD sessions |

### Report 2: ACD - Call Queue Summary Report

| RFP Requirement | Data Source | Calculation Method |
|----------------|-------------|-------------------|
| **Queue Name** | `dim_queues.queue_name` | Direct from queue dimension |
| **Calls** | `fact_calls` | `COUNT(*)` total calls received |
| **Calls Answered in 10s%** | `fact_calls.answered_within_10s` | `AVG(CASE WHEN answered_within_10s THEN 100.0 ELSE 0.0 END)` |
| **Abandoned** | `fact_calls.is_abandoned` | `SUM(CASE WHEN is_abandoned THEN 1 ELSE 0 END)` |
| **Logged in Time** | `fact_acd_sessions.session_duration_seconds` | `SUM(session_duration_seconds) / 3600` (hours) |
| **Agents Logged In** | `fact_acd_sessions` | `COUNT(DISTINCT agent_key)` |
| **Group Utilization%** | Calculated | `(Total Handle Time / Total Staffed Time) * 100` |

### Report 3: ACD - Call Taking Group Overview

| RFP Requirement | Data Source | Calculation Method |
|----------------|-------------|-------------------|
| **ACD Group** | `dim_queues.queue_name` | Direct from queue dimension |
| **Calls** | `fact_calls` | `COUNT(*)` total calls |
| **Calls Answered Within 10s/15s/20s/40s** | `fact_calls` boolean flags | `SUM(CASE WHEN answered_within_Xs THEN 1 ELSE 0 END)` |
| **Calls Abandoned** | `fact_calls.is_abandoned` | `SUM(CASE WHEN is_abandoned THEN 1 ELSE 0 END)` |
| **Calls Transferred** | `fact_calls.is_transferred` | `SUM(CASE WHEN is_transferred THEN 1 ELSE 0 END)` |
| **Service Level %** | `fact_calls` boolean flags | `AVG(CASE WHEN answered_within_Xs THEN 100.0 ELSE 0.0 END)` |
| **Logged in Time** | `fact_acd_sessions` | From ACD login/logout events |
| **Available Time** | `fact_agent_intervals` | From agent state aggregations |
| **Group Utilization%** | Calculated | `(Available Time / Logged in Time) * 100` |

## Data Calculation Details (CORRECTED)

### Where Values Are Calculated

**1. Call Metrics (from MariaDB callsummary - UNCHANGED):**
- **Source**: Existing Collector API processing (EC2 container)
- **Calculated in**: Collector API business logic (as it currently works)
- **Stored in**: MariaDB `callsummary` table (existing)
- **Transferred to**: Redshift `fact_calls_from_summary` via DMS/CDC
- **Examples**: Service level flags, call durations, transfer information, agent assignments

**2. Agent Session Metrics (from ACD Events - NEW Serverless):**
- **Source**: Agent events (Login, Logout, ACDLogin, ACDLogout)
- **Calculated in**: ACD Processor Lambda
- **Stored in**: `fact_agent_sessions`, `fact_acd_sessions`
- **Examples**: Session duration, logged in time per queue

**3. Agent State Metrics (from ACD Events - NEW Serverless):**
- **Source**: Agent state events (AgentAvailable, AgentBusiedOut)
- **Calculated in**: ACD Processor Lambda (5-minute intervals)
- **Stored in**: `fact_agent_intervals`, `fact_agent_states`
- **Examples**: Available time, staffed time, utilization percentages

**4. Data Integration (NEW - Links Call + Agent Data):**
- **Source**: MariaDB callsummary changes + ACD agent dimensions
- **Calculated in**: Data Integration Lambda
- **Stored in**: `fact_calls_from_summary` with agent_key/queue_key associations
- **Examples**: Linking call records with agent sessions via agent name matching

### Data Integration Strategy (CORRECTED)

**Key Challenge: Associating MariaDB Call Data with ACD Agent Data**

The main challenge is linking call records (from MariaDB callsummary) with agent session data (from ACD events) since they come from different sources.

**Association Strategy:**
1. **Agent Name Matching**: Use `callsummary.agentName` to match with `dim_agents.agent_name`
2. **Time-based Correlation**: Match call timestamps with agent session periods
3. **Queue Association**: Use `callsummary.tenantPsapName` to identify queue assignments

**Cross-Pipeline Joins:**
```sql
-- Example: Combining MariaDB call data with ACD agent session data
SELECT
    COALESCE(dq.queue_name, fc.tenant_psap_name) as queue_name,
    COUNT(fc.call_key) as calls_answered,
    SUM(COALESCE(fas.session_duration_seconds, 0)) / 3600.0 as logged_in_time_hours,
    CASE
        WHEN SUM(fas.session_duration_seconds) > 0
        THEN (SUM(fc.total_call_time_seconds) * 100.0) / SUM(fas.session_duration_seconds)
        ELSE 0
    END as utilization_percent
FROM fact_calls_from_summary fc  -- From MariaDB callsummary
LEFT JOIN dim_agents da ON fc.agent_name = da.agent_name AND fc.client_code = da.client_code
LEFT JOIN dim_queues dq ON fc.queue_key = dq.queue_key
LEFT JOIN fact_acd_sessions fas ON da.agent_key = fas.agent_key
    AND dq.queue_key = fas.queue_key
    AND DATE(fc.start_call_time) = DATE(fas.login_timestamp)  -- Time correlation
WHERE NOT COALESCE(fc.is_abandoned, false)
GROUP BY COALESCE(dq.queue_name, fc.tenant_psap_name);
```

**Data Quality Considerations:**
- **Missing Agent Sessions**: Calls may exist without corresponding ACD login events
- **Agent Name Variations**: Need consistent agent naming between callsummary and ACD events
- **Time Zone Handling**: Ensure consistent timestamp handling across systems

## Star Schema Design Benefits

### Power BI Optimization
1. **Dimension Tables**: Small, denormalized lookup tables
2. **Fact Tables**: Large tables with foreign keys to dimensions
3. **Pre-calculated Metrics**: Service level flags, durations in seconds
4. **Optimized Views**: Report-specific views for direct Power BI consumption

### Performance Features
1. **Distribution Keys**: Optimized for join performance
2. **Sort Keys**: Optimized for time-based queries
3. **Materialized Views**: Pre-aggregated data for faster reporting
4. **Partitioning**: Date-based partitioning for large fact tables

## Lambda Architecture Details (CORRECTED)

### 3 Specialized Lambda Functions

**1. Event Router Lambda (MODIFIED)**
- **Purpose**: Routes **only ACD/Agent events** to serverless processing
- **Triggers**: S3 events for i3logs files
- **Logic**: Filters out call events (they go to Collector API), routes only ACD events
- **Output**: SNS messages to ACD processing topic

**2. Data Integration Lambda (NEW)**
- **Purpose**: Processes MariaDB callsummary changes via DMS/CDC
- **Triggers**: SQS messages from DMS CDC events
- **Logic**: Transforms callsummary records, creates agent/queue dimensions, associates data
- **Output**: Inserts/updates to `fact_calls_from_summary` table

**3. ACD Processor Lambda (UNCHANGED)**
- **Purpose**: Processes agent/ACD events for real-time metrics
- **Triggers**: SQS messages from ACD events topic
- **Output**: Inserts to agent/queue fact tables, real-time status updates

### Scalability Features
- **Concurrent Execution**: Each Lambda scales independently
- **Dead Letter Queues**: Error handling and retry logic
- **Batch Processing**: Process multiple events per invocation
- **Connection Pooling**: Reuse database connections across invocations

## Client Isolation Strategy

### Infrastructure-Based Isolation
- **Redshift Serverless**: Separate namespace per client
- **Lambda Functions**: Client-specific environment variables
- **SQS Queues**: Client-specific message routing
- **IAM Roles**: Client-specific access permissions

### Data Isolation
- **Schema Separation**: `{client_code}_schema` per client
- **Client Code**: Every table includes `client_code` field
- **Message Filtering**: SNS message attributes for client routing

## Deployment and Monitoring

### Infrastructure as Code
- **Terraform**: Complete infrastructure provisioning
- **Client Configuration**: Variable-driven multi-client setup
- **Environment Management**: Dev, staging, production environments

### Monitoring and Alerting
- **CloudWatch Metrics**: Lambda duration, errors, SQS queue depth
- **Custom Metrics**: Event processing latency, data freshness
- **Alarms**: Critical failure notifications
- **Dashboards**: Operational visibility

## Key Architectural Benefits

### 1. **Minimal Disruption**
- **Collector API unchanged**: No risk to existing call processing
- **Proven call metrics**: Leverage existing, tested call summary calculations
- **Incremental migration**: Add ACD capabilities without touching core system

### 2. **Data Consistency**
- **Single source of truth**: MariaDB callsummary remains authoritative for call data
- **Real-time ACD data**: Independent processing for agent metrics
- **Flexible association**: Handle cases where agent data may be missing

### 3. **Scalability**
- **Serverless ACD processing**: Scales independently for agent events
- **CDC-based integration**: Efficient data transfer from MariaDB to Redshift
- **Client isolation**: Each client gets separate Redshift namespace

### 4. **Power BI Optimization**
- **Star schema design**: Optimized for analytical queries
- **Pre-calculated metrics**: Service levels already computed in MariaDB
- **Flexible joins**: Handle missing agent sessions gracefully

## Implementation Priority

1. **Phase 1**: Set up ACD event processing (Login/Logout/Available/BusiedOut)
2. **Phase 2**: Implement DMS CDC from MariaDB callsummary to Redshift
3. **Phase 3**: Create data integration layer to associate calls with agent sessions
4. **Phase 4**: Build Power BI reports with cross-pipeline joins
5. **Phase 5**: Add real-time dashboards and 5-minute interval processing

This **corrected architecture** provides a robust, scalable solution that respects the existing Collector API while adding powerful ACD/Agent reporting capabilities through targeted serverless processing.
